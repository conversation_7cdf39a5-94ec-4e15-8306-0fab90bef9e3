#!/usr/bin/env python3
"""
优化版本的图片拼接测试脚本
支持彩色图像和并行处理
"""

import os
import sys
import numpy as np
import pandas as pd
from PIL import Image
import time
from tqdm import tqdm
import multiprocessing

# 增加PIL的图像大小限制
Image.MAX_IMAGE_PIXELS = None

# 添加源代码路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))
import m2stitch
from m2stitch._image_utils import load_images_parallel, create_color_stitched_image


def load_images_optimized(folder_path, num_cols=16, num_rows=14, preserve_color=True):
    """
    优化的图片加载函数，支持彩色图像和并行处理
    """
    print("正在优化加载图片...")
    
    # 获取所有jpg文件
    image_files = []
    for i in range(1, num_cols * num_rows + 1):
        filename = f"s_{i:04d}.jpg"
        filepath = os.path.join(folder_path, filename)
        if os.path.exists(filepath):
            image_files.append(filepath)
        else:
            print(f"警告: 文件 {filename} 不存在")
    
    print(f"找到 {len(image_files)} 张图片")
    
    # 并行加载图片
    target_mode = 'RGB' if preserve_color else 'L'
    max_workers = min(8, multiprocessing.cpu_count())  # 限制线程数避免过载
    
    start_time = time.time()
    images = load_images_parallel(image_files, target_mode=target_mode, max_workers=max_workers)
    load_time = time.time() - start_time
    
    print(f"并行加载完成，耗时: {load_time:.2f} 秒")
    print(f"图片尺寸: {images.shape[1:]} ({'彩色' if preserve_color else '灰度'})")
    print(f"内存占用: {images.nbytes / 1024 / 1024:.2f} MB")
    
    # 生成Snake模式的行列索引
    rows = []
    cols = []
    
    for img_idx in range(len(image_files)):
        row = img_idx // num_cols
        if row % 2 == 0:  # 偶数行：从左到右
            col = img_idx % num_cols
        else:  # 奇数行：从右到左
            col = num_cols - 1 - (img_idx % num_cols)
        
        rows.append(row)
        cols.append(col)
    
    return images, rows, cols


def test_optimized_stitching(folder_path, preserve_color=True, use_parallel=True):
    """
    测试优化版本的图片拼接
    """
    print("=" * 70)
    print("M2Stitch 优化版本拼接测试")
    print("=" * 70)
    
    total_start = time.time()
    
    # 加载图片
    images, rows, cols = load_images_optimized(
        folder_path, 
        num_cols=16, 
        num_rows=14, 
        preserve_color=preserve_color
    )
    
    # 显示统计信息
    print(f"\n图片统计信息:")
    print(f"图片数量: {len(images)}")
    print(f"图片尺寸: {images.shape[1:]}")
    print(f"数据类型: {images.dtype}")
    print(f"颜色模式: {'RGB' if images.ndim == 4 else 'Grayscale'}")
    
    # 执行拼接算法
    print(f"\n开始执行优化拼接算法...")
    print(f"并行处理: {'启用' if use_parallel else '禁用'}")
    print(f"CPU核心数: {multiprocessing.cpu_count()}")
    
    stitch_start = time.time()
    
    try:
        # 使用优化版本的M2Stitch
        max_workers = multiprocessing.cpu_count() if use_parallel else 1
        
        result_df, prop_dict = m2stitch.stitch_images(
            images, 
            rows, 
            cols, 
            row_col_transpose=False,
            ncc_threshold=0.3,        # 降低阈值
            pou=5,                    # 增加重叠不确定性
            full_output=True,
            use_optimized=True,       # 使用优化算法
            max_workers=max_workers   # 并行处理
        )
        
        stitch_time = time.time() - stitch_start
        print(f"拼接完成，耗时: {stitch_time:.2f} 秒")
        
        # 显示结果
        print(f"\n拼接结果:")
        print(f"成功处理的图片数量: {len(result_df)}")
        print(result_df[['row', 'col', 'y_pos', 'x_pos']].head(10))
        
        # 显示拼接参数
        print(f"\n拼接参数:")
        for key, value in prop_dict.items():
            print(f"{key}: {value}")
        
        # 生成拼接图像
        print(f"\n生成最终拼接图像...")
        generate_optimized_stitched_image(images, result_df, preserve_color)
        
        total_time = time.time() - total_start
        print(f"\n总耗时: {total_time:.2f} 秒")
        
        return result_df, prop_dict
        
    except Exception as e:
        print(f"拼接过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return None, None


def generate_optimized_stitched_image(images, result_df, preserve_color=True, 
                                    output_path="stitched_result_optimized.png"):
    """
    生成优化的拼接图像，支持彩色和高效内存使用
    """
    # 计算相对位置
    result_df = result_df.copy()
    result_df["y_pos_rel"] = result_df["y_pos"] - result_df["y_pos"].min()
    result_df["x_pos_rel"] = result_df["x_pos"] - result_df["x_pos"].min()
    
    # 获取图片尺寸
    if images.ndim == 4:  # 彩色图像
        size_y, size_x = images.shape[1], images.shape[2]
        n_channels = images.shape[3]
    else:  # 灰度图像
        size_y, size_x = images.shape[1], images.shape[2]
        n_channels = 1
    
    # 计算拼接图像尺寸
    stitched_height = int(result_df["y_pos_rel"].max() + size_y)
    stitched_width = int(result_df["x_pos_rel"].max() + size_x)
    
    print(f"拼接图像尺寸: {stitched_width} x {stitched_height}")
    print(f"预估文件大小: {stitched_width * stitched_height * n_channels / 1024 / 1024:.2f} MB")
    
    if preserve_color and images.ndim == 4:
        # 使用优化的彩色拼接
        positions = result_df[["y_pos_rel", "x_pos_rel"]].values
        stitched_image = create_color_stitched_image(
            images, 
            positions, 
            (stitched_height, stitched_width),
            blend_mode='overlay'
        )
        
        # 保存彩色结果
        if stitched_image.shape[2] == 3:
            result_image = Image.fromarray(stitched_image.astype(np.uint8), 'RGB')
        else:
            result_image = Image.fromarray(stitched_image.astype(np.uint8))
    else:
        # 灰度图像处理
        if images.ndim == 4:
            # 转换彩色图像为灰度
            from m2stitch._image_utils import prepare_image_for_stitching
            gray_images = np.zeros((images.shape[0], size_y, size_x), dtype=np.uint8)
            for i in range(images.shape[0]):
                gray_images[i] = prepare_image_for_stitching(images[i])
            images = gray_images
        
        # 创建灰度拼接图像
        stitched_image = np.zeros((stitched_height, stitched_width), dtype=np.uint8)
        
        # 放置每张图片
        for i, row in tqdm(result_df.iterrows(), total=len(result_df), desc="生成拼接图像"):
            y_start = int(row["y_pos_rel"])
            y_end = y_start + size_y
            x_start = int(row["x_pos_rel"])
            x_end = x_start + size_x
            
            stitched_image[y_start:y_end, x_start:x_end] = images[i]
        
        result_image = Image.fromarray(stitched_image, 'L')
    
    # 保存结果
    result_image.save(output_path)
    print(f"拼接图像已保存到: {output_path}")
    
    # 生成缩略图
    thumbnail_size = (2000, 2000)
    result_image.thumbnail(thumbnail_size, Image.Resampling.LANCZOS)
    thumbnail_path = output_path.replace('.png', '_thumbnail.png')
    result_image.save(thumbnail_path)
    print(f"缩略图已保存到: {thumbnail_path}")
    
    return stitched_image


def compare_performance():
    """
    比较原版和优化版的性能
    """
    folder_path = r"D:\images\image_55"
    
    if not os.path.exists(folder_path):
        print(f"错误: 文件夹 {folder_path} 不存在")
        return
    
    print("=" * 70)
    print("性能对比测试")
    print("=" * 70)
    
    # 测试优化版本（彩色）
    print("\n1. 测试优化版本（彩色图像）")
    result_opt_color, _ = test_optimized_stitching(folder_path, preserve_color=True, use_parallel=True)
    
    # 测试优化版本（灰度）
    print("\n2. 测试优化版本（灰度图像）")
    result_opt_gray, _ = test_optimized_stitching(folder_path, preserve_color=False, use_parallel=True)
    
    print("\n" + "=" * 70)
    print("性能对比完成！")
    print("=" * 70)


if __name__ == "__main__":
    folder_path = r"D:\images\image_55"
    
    if not os.path.exists(folder_path):
        print(f"错误: 文件夹 {folder_path} 不存在")
        exit(1)
    
    # 执行优化测试
    print("选择测试模式:")
    print("1. 彩色图像拼接（推荐）")
    print("2. 灰度图像拼接")
    print("3. 性能对比测试")
    
    choice = input("请输入选择 (1-3): ").strip()
    
    if choice == "1":
        test_optimized_stitching(folder_path, preserve_color=True, use_parallel=True)
    elif choice == "2":
        test_optimized_stitching(folder_path, preserve_color=False, use_parallel=True)
    elif choice == "3":
        compare_performance()
    else:
        print("默认使用彩色图像拼接")
        test_optimized_stitching(folder_path, preserve_color=True, use_parallel=True)
