#!/usr/bin/env python3
"""
超高速图像拼接系统
专注于速度和文件大小优化

优化策略：
1. 跳过复杂的相位相关计算，直接使用网格估算
2. 只对关键图像对进行精确计算
3. 使用JPEG压缩输出
4. 内存优化和并行处理
"""

import os
import numpy as np
from PIL import Image
import time
from tqdm import tqdm
import concurrent.futures
from typing import List, Tuple
import cv2


class UltraFastStitcher:
    """超高速图像拼接器"""
    
    def __init__(self, overlap_ratio=0.1, max_workers=4, jpeg_quality=85):
        """
        初始化
        
        Parameters:
        -----------
        overlap_ratio : float
            重叠比例，默认0.1 (10%)
        max_workers : int
            最大工作线程数
        jpeg_quality : int
            JPEG压缩质量 (1-100)
        """
        self.overlap_ratio = overlap_ratio
        self.max_workers = max_workers
        self.jpeg_quality = jpeg_quality
        Image.MAX_IMAGE_PIXELS = None
        
    def ultra_fast_stitch(self, folder_path: str, num_cols: int = 16, num_rows: int = 14,
                         output_path: str = "ultra_fast_result.jpg"):
        """
        超高速拼接主函数
        """
        print("=" * 60)
        print("超高速图像拼接系统")
        print("=" * 60)
        
        total_start = time.time()
        
        # 1. 快速加载和分析
        print("1. 快速加载图像...")
        images, grid_info = self._load_and_analyze(folder_path, num_cols, num_rows)
        
        # 2. 智能位置估算（跳过复杂计算）
        print("2. 智能位置估算...")
        positions = self._estimate_positions_smart(images, grid_info, num_cols, num_rows)
        
        # 3. 高效拼接和压缩保存
        print("3. 高效拼接和保存...")
        self._stitch_and_save_compressed(images, positions, output_path)
        
        total_time = time.time() - total_start
        print(f"\n✅ 总耗时: {total_time:.2f} 秒")
        print(f"📁 输出文件: {output_path}")
        
        # 显示文件大小
        if os.path.exists(output_path):
            file_size = os.path.getsize(output_path) / (1024 * 1024)
            print(f"📊 文件大小: {file_size:.2f} MB")
        
        return positions
    
    def _load_and_analyze(self, folder_path: str, num_cols: int, num_rows: int):
        """快速加载和分析图像"""
        
        # 获取文件列表
        image_files = []
        for i in range(1, num_cols * num_rows + 1):
            filepath = os.path.join(folder_path, f"s_{i:04d}.jpg")
            if os.path.exists(filepath):
                image_files.append(filepath)
        
        print(f"📷 找到 {len(image_files)} 张图像")
        
        # 快速加载（使用OpenCV更快）
        def load_image_cv2(filepath):
            img = cv2.imread(filepath)
            if img is not None:
                return cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
            else:
                # 备用PIL加载
                with Image.open(filepath) as pil_img:
                    return np.array(pil_img.convert('RGB'))
        
        start_time = time.time()
        
        # 先加载第一张确定尺寸
        first_img = load_image_cv2(image_files[0])
        img_height, img_width = first_img.shape[:2]
        
        print(f"📐 图像尺寸: {img_width} x {img_height}")
        
        # 并行加载所有图像
        images = np.zeros((len(image_files), img_height, img_width, 3), dtype=np.uint8)
        images[0] = first_img
        
        if len(image_files) > 1:
            with concurrent.futures.ThreadPoolExecutor(max_workers=self.max_workers) as executor:
                futures = {executor.submit(load_image_cv2, filepath): i 
                          for i, filepath in enumerate(image_files[1:], 1)}
                
                for future in tqdm(concurrent.futures.as_completed(futures), 
                                 total=len(futures), desc="⚡ 加载"):
                    idx = futures[future]
                    images[idx] = future.result()
        
        load_time = time.time() - start_time
        print(f"⏱️  加载耗时: {load_time:.2f} 秒")
        
        # 生成网格信息
        grid_info = []
        for img_idx in range(len(image_files)):
            row = img_idx // num_cols
            if row % 2 == 0:  # 偶数行：从左到右
                col = img_idx % num_cols
            else:  # 奇数行：从右到左
                col = num_cols - 1 - (img_idx % num_cols)
            grid_info.append((row, col, img_idx))
        
        return images, grid_info
    
    def _estimate_positions_smart(self, images: np.ndarray, grid_info: List[Tuple], 
                                num_cols: int, num_rows: int) -> np.ndarray:
        """智能位置估算（避免复杂计算）"""
        
        img_height, img_width = images.shape[1], images.shape[2]
        
        # 基于重叠比例计算步长
        step_x = int(img_width * (1 - self.overlap_ratio))
        step_y = int(img_height * (1 - self.overlap_ratio))
        
        print(f"📏 估算步长: X={step_x}px, Y={step_y}px (重叠{self.overlap_ratio*100:.1f}%)")
        
        # 只对少数关键图像对进行精确计算来微调
        key_adjustments = self._compute_key_adjustments(images, grid_info, num_cols, num_rows)
        
        # 计算所有图像位置
        positions = np.zeros((len(images), 2), dtype=int)
        
        for row, col, img_idx in grid_info:
            # 基础网格位置
            base_y = row * step_y
            base_x = col * step_x
            
            # 应用微调（如果有）
            adj_y, adj_x = key_adjustments.get((row, col), (0, 0))
            
            positions[img_idx] = [base_y + adj_y, base_x + adj_x]
        
        return positions
    
    def _compute_key_adjustments(self, images: np.ndarray, grid_info: List[Tuple], 
                               num_cols: int, num_rows: int) -> dict:
        """只计算关键图像对的精确位移"""
        
        # 选择关键位置进行精确计算（大大减少计算量）
        key_positions = [
            (0, 0), (0, num_cols//2), (0, num_cols-1),  # 第一行的几个关键点
            (num_rows//2, 0), (num_rows//2, num_cols//2),  # 中间行的关键点
            (num_rows-1, 0), (num_rows-1, num_cols-1)  # 最后一行的关键点
        ]
        
        # 创建位置到索引的映射
        pos_to_idx = {(row, col): img_idx for row, col, img_idx in grid_info}
        
        adjustments = {}
        
        print(f"🔍 精确计算 {len(key_positions)} 个关键位置...")
        
        for row, col in tqdm(key_positions, desc="🎯 关键点"):
            if (row, col) not in pos_to_idx:
                continue
                
            # 与右邻居比较
            if col < num_cols - 1 and (row, col + 1) in pos_to_idx:
                idx1 = pos_to_idx[(row, col)]
                idx2 = pos_to_idx[(row, col + 1)]
                
                # 快速模板匹配
                adj = self._quick_template_match(images[idx1], images[idx2], 'horizontal')
                adjustments[(row, col + 1)] = adj
            
            # 与下邻居比较
            if row < num_rows - 1 and (row + 1, col) in pos_to_idx:
                idx1 = pos_to_idx[(row, col)]
                idx2 = pos_to_idx[(row + 1, col)]
                
                # 快速模板匹配
                adj = self._quick_template_match(images[idx1], images[idx2], 'vertical')
                adjustments[(row + 1, col)] = adj
        
        return adjustments
    
    def _quick_template_match(self, img1: np.ndarray, img2: np.ndarray, direction: str) -> Tuple[int, int]:
        """快速模板匹配（比相位相关快得多）"""
        
        # 转换为灰度
        gray1 = cv2.cvtColor(img1, cv2.COLOR_RGB2GRAY)
        gray2 = cv2.cvtColor(img2, cv2.COLOR_RGB2GRAY)
        
        # 降采样加速
        scale = 4
        small1 = gray1[::scale, ::scale]
        small2 = gray2[::scale, ::scale]
        
        if direction == 'horizontal':
            # 水平匹配：使用右边缘作为模板
            template = small1[:, -small1.shape[1]//4:]
            search_area = small2[:, :small2.shape[1]//2]
        else:  # vertical
            # 垂直匹配：使用下边缘作为模板
            template = small1[-small1.shape[0]//4:, :]
            search_area = small2[:small2.shape[0]//2, :]
        
        # 模板匹配
        if template.size > 0 and search_area.size > 0:
            result = cv2.matchTemplate(search_area, template, cv2.TM_CCOEFF_NORMED)
            _, max_val, _, max_loc = cv2.minMaxLoc(result)
            
            if max_val > 0.3:  # 置信度阈值
                # 转换回原尺寸
                if direction == 'horizontal':
                    return (0, max_loc[0] * scale)
                else:
                    return (max_loc[1] * scale, 0)
        
        return (0, 0)
    
    def _stitch_and_save_compressed(self, images: np.ndarray, positions: np.ndarray, output_path: str):
        """高效拼接和压缩保存"""
        
        img_height, img_width = images.shape[1], images.shape[2]
        
        # 计算输出尺寸
        max_y = positions[:, 0].max() + img_height
        max_x = positions[:, 1].max() + img_width
        
        print(f"📐 输出尺寸: {max_x} x {max_y}")
        
        # 检查内存需求
        memory_mb = (max_y * max_x * 3) / (1024 * 1024)
        print(f"💾 内存需求: {memory_mb:.1f} MB")
        
        if memory_mb > 4000:  # 如果超过4GB，使用分块处理
            print("⚠️  图像过大，使用分块处理...")
            self._stitch_large_image_chunked(images, positions, output_path)
        else:
            # 直接处理
            output = np.zeros((max_y, max_x, 3), dtype=np.uint8)
            
            # 拼接
            for i, (y_pos, x_pos) in enumerate(tqdm(positions, desc="🧩 拼接")):
                y_end = min(y_pos + img_height, max_y)
                x_end = min(x_pos + img_width, max_x)
                
                output[y_pos:y_end, x_pos:x_end] = images[i][:y_end-y_pos, :x_end-x_pos]
            
            # 保存为JPEG
            self._save_as_jpeg(output, output_path)
    
    def _stitch_large_image_chunked(self, images: np.ndarray, positions: np.ndarray, output_path: str):
        """分块处理大图像"""
        
        img_height, img_width = images.shape[1], images.shape[2]
        max_y = positions[:, 0].max() + img_height
        max_x = positions[:, 1].max() + img_width
        
        # 分块大小
        chunk_size = 8192  # 8K块
        
        print(f"🔄 分块处理: {chunk_size}x{chunk_size} 像素块")
        
        # 创建输出图像文件
        output = np.zeros((max_y, max_x, 3), dtype=np.uint8)
        
        # 按块处理
        for y_start in tqdm(range(0, max_y, chunk_size), desc="📦 分块"):
            y_end = min(y_start + chunk_size, max_y)
            
            for x_start in range(0, max_x, chunk_size):
                x_end = min(x_start + chunk_size, max_x)
                
                # 处理当前块
                chunk = np.zeros((y_end - y_start, x_end - x_start, 3), dtype=np.uint8)
                
                # 找到与当前块重叠的图像
                for i, (img_y, img_x) in enumerate(positions):
                    if (img_y < y_end and img_y + img_height > y_start and
                        img_x < x_end and img_x + img_width > x_start):
                        
                        # 计算重叠区域
                        overlap_y_start = max(0, img_y - y_start)
                        overlap_y_end = min(y_end - y_start, img_y + img_height - y_start)
                        overlap_x_start = max(0, img_x - x_start)
                        overlap_x_end = min(x_end - x_start, img_x + img_width - x_start)
                        
                        img_y_start = max(0, y_start - img_y)
                        img_y_end = img_y_start + (overlap_y_end - overlap_y_start)
                        img_x_start = max(0, x_start - img_x)
                        img_x_end = img_x_start + (overlap_x_end - overlap_x_start)
                        
                        if (overlap_y_end > overlap_y_start and overlap_x_end > overlap_x_start):
                            chunk[overlap_y_start:overlap_y_end, overlap_x_start:overlap_x_end] = \
                                images[i][img_y_start:img_y_end, img_x_start:img_x_end]
                
                output[y_start:y_end, x_start:x_end] = chunk
        
        # 保存
        self._save_as_jpeg(output, output_path)
    
    def _save_as_jpeg(self, image: np.ndarray, output_path: str):
        """保存为压缩JPEG"""
        
        print(f"💾 保存为JPEG (质量: {self.jpeg_quality})...")
        
        # 使用PIL保存JPEG
        pil_image = Image.fromarray(image, 'RGB')
        pil_image.save(output_path, 'JPEG', quality=self.jpeg_quality, optimize=True)
        
        # 同时创建小缩略图
        thumbnail_path = output_path.replace('.jpg', '_thumb.jpg')
        pil_image.thumbnail((1500, 1500), Image.Resampling.LANCZOS)
        pil_image.save(thumbnail_path, 'JPEG', quality=80, optimize=True)
        
        print(f"✅ 已保存: {output_path}")
        print(f"🖼️  缩略图: {thumbnail_path}")


def main():
    """主函数"""
    folder_path = r"D:\images\image_55"
    
    if not os.path.exists(folder_path):
        print(f"❌ 错误: 文件夹 {folder_path} 不存在")
        return
    
    # 创建超高速拼接器
    stitcher = UltraFastStitcher(
        overlap_ratio=0.1,    # 10% 重叠
        max_workers=6,        # 6个工作线程
        jpeg_quality=85       # JPEG质量85%
    )
    
    # 执行超高速拼接
    positions = stitcher.ultra_fast_stitch(
        folder_path=folder_path,
        num_cols=16,
        num_rows=14,
        output_path="ultra_fast_result.jpg"
    )
    
    print("🎉 拼接完成！")


if __name__ == "__main__":
    main()
