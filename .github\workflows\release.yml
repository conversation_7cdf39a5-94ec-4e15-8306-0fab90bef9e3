name: Release

on:
  push:
    branches:
      - main
      - master

jobs:
  release:
    name: Release
    runs-on: ubuntu-latest
    steps:
      - name: Check out the repository
        uses: actions/checkout@v3
        with:
          fetch-depth: 2

      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: "3.9"

      - name: Upgrade pip
        run: |
          pip install --constraint=.github/workflows/constraints.txt pip
          pip --version

      - name: Install Poetry
        run: |
          pip install --constraint=.github/workflows/constraints.txt poetry
          poetry --version

      - name: Check if there is a parent commit
        id: check-parent-commit
        run: |
          echo "::set-output name=sha::$(git rev-parse --verify --quiet HEAD^)"

      - name: Detect and tag new version
        id: check-version
        if: steps.check-parent-commit.outputs.sha
        uses: salsify/action-detect-and-tag-new-version@v2.0.3
        with:
          version-command: |
            bash -o pipefail -c "poetry version | awk '{ print \$2 }'"

      - name: Bump version for developmental release
        if: "! steps.check-version.outputs.tag"
        run: |
          poetry version patch &&
          version=$(poetry version | awk '{ print $2 }') &&
          poetry version $version.dev.$(date +%s)

      - name: Build package
        run: |
          poetry build --ansi

      - name: Publish package on PyPI
        if: steps.check-version.outputs.tag
        uses: pypa/gh-action-pypi-publish@v1.8.11
        with:
          user: __token__
          password: ${{ secrets.PYPI_TOKEN }}

      - name: Publish package on TestPyPI
        if: "! steps.check-version.outputs.tag"
        uses: pypa/gh-action-pypi-publish@v1.8.11
        with:
          user: __token__
          password: ${{ secrets.TEST_PYPI_TOKEN }}
          repository_url: https://test.pypi.org/legacy/

      - name: Publish the release notes
        uses: release-drafter/release-drafter@v5.23.0
        with:
          publish: ${{ steps.check-version.outputs.tag != '' }}
          tag: ${{ steps.check-version.outputs.tag }}
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
