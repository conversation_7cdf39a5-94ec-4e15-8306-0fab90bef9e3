[tool.poetry]
name = "m2stitch"
version = "0.7.0"
description = "M2Stitch"
authors = ["<PERSON><PERSON><PERSON> <<EMAIL>>"]
license = "MIT"
readme = "README.rst"
homepage = "https://github.com/yfukai/m2stitch"
repository = "https://github.com/yfukai/m2stitch"
documentation = "https://m2stitch.readthedocs.io"
classifiers = [
    "Development Status :: 3 - Alpha",
    "Intended Audience :: Developers",
    "Intended Audience :: Science/Research",
    "Natural Language :: English",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Programming Language :: Python :: 3.13",
    "License :: OSI Approved :: MIT License",
]

[tool.poetry.urls]
Changelog = "https://github.com/yfukai/m2stitch/releases"

[tool.poetry.dependencies]
python = ">=3.8,<3.15"
click = ">=7,<9"
numpy = ">=1.22,<3.0"
pandas = ">=1.2.4,<3.0.0"
pandas-stubs = ">=1.2.0,<2.0"
networkx = ">=2.5.1,<4.0"
scipy = ">=1.6.3,<2.0"
tqdm = ">=4.60.0,<5.0"
scikit-learn = ">=1.0.2,<2.0"

[tool.poetry.dev-dependencies]
pytest = "^7.1.3"
pytest-datadir = "^1.4.1"
pytest-profiling = "^1.7.0"
coverage = {extras = ["toml"], version = "^6.5"}
safety = "^2.3.1"
mypy = "^0.982"
typeguard = "^2.13.3"
xdoctest = {extras = ["colors"], version = "^1.1.0"}
sphinx = "^5.1.1"
sphinx-autobuild = "^2021.3.14"
pre-commit = "^2.20.0"
#flake8 = "^4.0.1"
#black = "^21.12b0"
#flake8-bandit = "^2.1.2"
#flake8-bugbear = "^21.4.3"
#flake8-docstrings = "^1.6.0"
#flake8-rst-docstrings = "^0.2.5"
#pep8-naming = "^0.12.0"
#darglint = "^1.8.1"
#reorder-python-imports = "^2.6.0"
pre-commit-hooks = "^4.3.0"
sphinx-rtd-theme = "^1.0.0"
sphinx-click = "^4.3.0"
Pygments = "^2.13.0"

[tool.poetry.scripts]
m2stitch = "m2stitch.__main__:main"

[tool.coverage.paths]
source = ["src", "*/site-packages"]

[tool.coverage.run]
branch = true
source = ["m2stitch"]

[tool.coverage.report]
show_missing = true
fail_under = 90

[build-system]
requires = ["poetry-core>=1.0.0"]
build-backend = "poetry.core.masonry.api"

[mypy]
plugins = "numpy.typing.mypy_plugin"
