#!/usr/bin/env python3
"""
分析拼接结果的脚本
"""

import os
import sys
import numpy as np
import pandas as pd
from PIL import Image
import matplotlib.pyplot as plt
from tqdm import tqdm

# 增加PIL的图像大小限制
Image.MAX_IMAGE_PIXELS = None

# 添加源代码路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))
import m2stitch

def analyze_stitching_results():
    """分析拼接结果"""
    
    print("=" * 60)
    print("M2Stitch 拼接结果分析")
    print("=" * 60)
    
    # 检查结果文件
    result_files = [
        "stitched_result.png",
        "stitched_result_thumbnail.png"
    ]
    
    for filename in result_files:
        if os.path.exists(filename):
            file_size = os.path.getsize(filename) / (1024 * 1024)  # MB
            print(f"✓ {filename}: {file_size:.2f} MB")
            
            # 获取图像信息
            with Image.open(filename) as img:
                print(f"  尺寸: {img.width} x {img.height}")
                print(f"  模式: {img.mode}")
        else:
            print(f"✗ {filename}: 文件不存在")
    
    print("\n" + "=" * 60)
    print("拼接参数分析")
    print("=" * 60)
    
    # 重新运行拼接以获取详细信息
    folder_path = r"D:\images\image_55"
    
    # 快速加载几张图片进行分析
    sample_images = []
    for i in range(1, 6):  # 加载前5张图片
        filename = f"s_{i:04d}.jpg"
        filepath = os.path.join(folder_path, filename)
        if os.path.exists(filepath):
            img = Image.open(filepath)
            if img.mode != 'L':
                img = img.convert('L')
            sample_images.append(np.array(img))
    
    if sample_images:
        sample_images = np.array(sample_images)
        print(f"样本图片信息:")
        print(f"  数量: {len(sample_images)}")
        print(f"  尺寸: {sample_images.shape[1]} x {sample_images.shape[2]}")
        print(f"  数据类型: {sample_images.dtype}")
        
        # 分析图像质量
        print(f"\n图像质量分析:")
        for i, img in enumerate(sample_images):
            mean_val = np.mean(img)
            std_val = np.std(img)
            min_val = np.min(img)
            max_val = np.max(img)
            print(f"  图片 {i+1}: 均值={mean_val:.1f}, 标准差={std_val:.1f}, 范围=[{min_val}, {max_val}]")
    
    print("\n" + "=" * 60)
    print("拼接算法性能总结")
    print("=" * 60)
    
    # 从之前的输出中提取的信息
    print("✓ 成功处理: 224张图片 (16列 x 14行)")
    print("✓ 图片尺寸: 2448 x 2048 像素")
    print("✓ 总内存使用: ~1071 MB")
    print("✓ 拼接耗时: ~22分钟")
    print("✓ 最终图像尺寸: 35367 x 25982 像素")
    print("✓ 检测到的重叠率:")
    print("  - 水平重叠: ~10.38%")
    print("  - 垂直重叠: ~10.45%")
    print("✓ 重复性参数: 15.0")
    
    print("\n" + "=" * 60)
    print("Snake模式验证")
    print("=" * 60)
    
    # 验证Snake模式的正确性
    num_cols, num_rows = 16, 14
    print("Snake模式行进路径验证:")
    
    for row in range(min(5, num_rows)):  # 显示前5行
        row_path = []
        for img_idx in range(row * num_cols, (row + 1) * num_cols):
            if row % 2 == 0:  # 偶数行：从左到右
                col = img_idx % num_cols
            else:  # 奇数行：从右到左
                col = num_cols - 1 - (img_idx % num_cols)
            row_path.append(f"({row},{col})")
        
        direction = "→" if row % 2 == 0 else "←"
        print(f"  行 {row} {direction}: {' '.join(row_path[:8])}{'...' if len(row_path) > 8 else ''}")
    
    print("\n" + "=" * 60)
    print("建议和优化")
    print("=" * 60)
    
    print("✓ 拼接成功完成，算法表现良好")
    print("✓ 检测到的重叠率与预期的10%非常接近")
    print("✓ Snake模式正确处理")
    print("\n改进建议:")
    print("  1. 可以尝试调整ncc_threshold参数来优化配准质量")
    print("  2. 对于大图像，可以考虑分块处理以减少内存使用")
    print("  3. 可以添加图像融合算法来改善重叠区域的视觉效果")
    print("  4. 考虑使用多线程来加速处理")
    
    return True

def create_visualization():
    """创建可视化图表"""
    print("\n" + "=" * 60)
    print("创建可视化图表")
    print("=" * 60)
    
    try:
        # 创建网格布局可视化
        num_cols, num_rows = 16, 14
        
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 8))
        
        # 子图1：显示Snake模式路径
        grid = np.zeros((num_rows, num_cols))
        for img_idx in range(num_cols * num_rows):
            row = img_idx // num_cols
            if row % 2 == 0:
                col = img_idx % num_cols
            else:
                col = num_cols - 1 - (img_idx % num_cols)
            grid[row, col] = img_idx + 1
        
        im1 = ax1.imshow(grid, cmap='viridis', aspect='equal')
        ax1.set_title('Snake模式图片编号分布')
        ax1.set_xlabel('列')
        ax1.set_ylabel('行')
        
        # 添加数字标注
        for i in range(num_rows):
            for j in range(num_cols):
                ax1.text(j, i, f'{int(grid[i, j])}', 
                        ha='center', va='center', color='white', fontsize=6)
        
        plt.colorbar(im1, ax=ax1, label='图片编号')
        
        # 子图2：显示重叠区域示意图
        overlap_x = 10.38  # 水平重叠百分比
        overlap_y = 10.45  # 垂直重叠百分比
        
        # 创建重叠示意图
        tile_width = 100
        tile_height = 80
        
        # 绘制4个相邻的瓦片
        tiles = [
            (0, 0, 'Tile 1'),
            (tile_width * (100 - overlap_x) / 100, 0, 'Tile 2'),
            (0, tile_height * (100 - overlap_y) / 100, 'Tile 3'),
            (tile_width * (100 - overlap_x) / 100, tile_height * (100 - overlap_y) / 100, 'Tile 4')
        ]
        
        colors = ['lightblue', 'lightgreen', 'lightcoral', 'lightyellow']
        
        for i, (x, y, label) in enumerate(tiles):
            rect = plt.Rectangle((x, y), tile_width, tile_height, 
                               facecolor=colors[i], alpha=0.7, edgecolor='black')
            ax2.add_patch(rect)
            ax2.text(x + tile_width/2, y + tile_height/2, label, 
                    ha='center', va='center', fontweight='bold')
        
        ax2.set_xlim(-10, tile_width * 2)
        ax2.set_ylim(-10, tile_height * 2)
        ax2.set_aspect('equal')
        ax2.set_title(f'重叠区域示意图\n水平重叠: {overlap_x:.1f}%, 垂直重叠: {overlap_y:.1f}%')
        ax2.set_xlabel('X方向')
        ax2.set_ylabel('Y方向')
        
        plt.tight_layout()
        plt.savefig('stitching_analysis.png', dpi=150, bbox_inches='tight')
        print("✓ 可视化图表已保存到: stitching_analysis.png")
        
        return True
        
    except Exception as e:
        print(f"创建可视化时出错: {e}")
        return False

if __name__ == "__main__":
    # 执行分析
    success = analyze_stitching_results()
    
    if success:
        # 创建可视化
        create_visualization()
        
        print("\n" + "=" * 60)
        print("分析完成！")
        print("=" * 60)
        print("生成的文件:")
        print("  - stitched_result.png (完整拼接图像)")
        print("  - stitched_result_thumbnail.png (缩略图)")
        print("  - stitching_analysis.png (分析图表)")
        print("\n建议使用图像查看器打开缩略图查看拼接效果。")
    else:
        print("分析失败！")
