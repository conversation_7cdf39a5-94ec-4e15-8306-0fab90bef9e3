"""Optimized stitching algorithms with parallel processing and memory efficiency."""

import numpy as np
import pandas as pd
from typing import Union, Sequence, Optional, Tuple, Any
import concurrent.futures
from functools import partial
import warnings
from tqdm import tqdm

from ._translation_computation import pcm, interpret_translation, multi_peak_max
from ._stage_model import compute_image_overlap2, filter_by_overlap_and_correlation
from ._stage_model import filter_by_repeatability, filter_outliers, replace_invalid_translations
from ._constrained_refinement import refine_translations
from ._global_optimization import compute_maximum_spanning_tree, compute_final_position
from ._typing_utils import NumArray, Float
from ._image_utils import prepare_image_for_stitching


class ElipticEnvelopPredictor:
    """Optimized elliptic envelope predictor with caching."""
    
    def __init__(self, contamination=0.4, epsilon=0.01, random_seed=0):
        from sklearn.covariance import EllipticEnvelope
        self.predictor = EllipticEnvelope(
            contamination=contamination, 
            random_state=random_seed
        )
        self.fitted = False
        
    def __call__(self, data):
        if not self.fitted:
            self.predictor.fit(data)
            self.fitted = True
        return self.predictor.predict(data)


def compute_translation_parallel(args):
    """Compute translation for a single image pair (for parallel processing)."""
    i1, i2, image1, image2, direction, lims, ncc_threshold = args
    
    try:
        PCM = pcm(image1, image2).real
        yins, xins, _ = multi_peak_max(PCM)
        max_peak = interpret_translation(
            image1, image2, yins, xins, *lims[0], *lims[1]
        )
        
        return {
            'i2': i2,
            'direction': direction,
            'ncc': max_peak[0],
            'y': max_peak[1],
            'x': max_peak[2],
            'success': True
        }
    except Exception as e:
        return {
            'i2': i2,
            'direction': direction,
            'ncc': 0.0,
            'y': 0,
            'x': 0,
            'success': False,
            'error': str(e)
        }


def stitch_images_optimized(
    images: Union[Sequence[NumArray], NumArray],
    rows: Optional[Sequence[Any]] = None,
    cols: Optional[Sequence[Any]] = None,
    position_indices: Optional[NumArray] = None,
    position_initial_guess: Optional[NumArray] = None,
    overlap_diff_threshold: Float = 10,
    pou: Float = 3,
    full_output: bool = False,
    row_col_transpose: bool = True,
    ncc_threshold: Float = 0.5,
    max_workers: Optional[int] = None,
    use_parallel: bool = True,
    memory_efficient: bool = True,
) -> Tuple[pd.DataFrame, dict]:
    """
    Optimized version of stitch_images with parallel processing and memory efficiency.
    
    Parameters
    ----------
    images : np.ndarray or sequence
        The images to stitch
    rows : list, optional
        Row indices of the images
    cols : list, optional  
        Column indices of the images
    position_indices : np.ndarray, optional
        Tile position indices
    position_initial_guess : np.ndarray, optional
        Initial guess for positions
    overlap_diff_threshold : float, default 10
        Allowed difference from initial guess in percentage
    pou : float, default 3
        Percent overlap uncertainty parameter
    full_output : bool, default False
        Return full computation result
    row_col_transpose : bool, default True
        Switch row and col indices for compatibility
    ncc_threshold : float, default 0.5
        Normalized cross correlation threshold
    max_workers : int, optional
        Maximum number of parallel workers
    use_parallel : bool, default True
        Enable parallel processing
    memory_efficient : bool, default True
        Use memory-efficient algorithms
        
    Returns
    -------
    grid : pd.DataFrame
        Result dataframe with positions
    prop_dict : dict
        Dictionary of estimated parameters
    """
    # Convert and validate inputs
    images = np.array(images)
    
    # Prepare images for stitching (convert to grayscale if needed)
    if memory_efficient:
        processed_images = np.zeros((images.shape[0], images.shape[1], images.shape[2]), 
                                   dtype=np.uint8)
        for i in range(images.shape[0]):
            processed_images[i] = prepare_image_for_stitching(images[i])
        images = processed_images
    
    assert (position_indices is not None) or (rows is not None and cols is not None)
    if position_indices is None:
        if row_col_transpose:
            warnings.warn(
                "row_col_transpose is True. The default value will be changed to False in the major release."
            )
            position_indices = np.array([cols, rows]).T
        else:
            position_indices = np.array([rows, cols]).T
    
    position_indices = np.array(position_indices)
    assert images.shape[0] == position_indices.shape[0]
    assert position_indices.shape[1] == images.ndim - 1
    
    if position_initial_guess is not None:
        position_initial_guess = np.array(position_initial_guess)
        assert images.shape[0] == position_indices.shape[0]
        assert position_initial_guess.shape[1] == images.ndim - 1
    
    assert 0 <= overlap_diff_threshold and overlap_diff_threshold <= 100
    _rows, _cols = position_indices.T

    sizeY, sizeX = images.shape[1:]

    grid = pd.DataFrame(
        {
            "col": _cols,
            "row": _rows,
        },
        index=np.arange(len(_cols)),
    )

    # Build neighbor relationships more efficiently
    grid_dict = {}
    for i, (row, col) in enumerate(zip(_rows, _cols)):
        grid_dict[(row, col)] = i

    # Add neighbor information
    for i, (row, col) in enumerate(zip(_rows, _cols)):
        # Left neighbor
        left_key = (row, col - 1)
        grid.loc[i, "left"] = grid_dict.get(left_key, np.nan)
        
        # Top neighbor  
        top_key = (row - 1, col)
        grid.loc[i, "top"] = grid_dict.get(top_key, np.nan)

    # Add initial guess information if provided
    if position_initial_guess is not None:
        for i, pos_guess in enumerate(position_initial_guess):
            grid.loc[i, "y_init_guess"] = pos_guess[0]
            grid.loc[i, "x_init_guess"] = pos_guess[1]
            
            # Compute relative guesses for neighbors
            for direction in ["left", "top"]:
                neighbor_idx = grid.loc[i, direction]
                if not pd.isna(neighbor_idx):
                    neighbor_idx = int(neighbor_idx)
                    neighbor_pos = position_initial_guess[neighbor_idx]
                    rel_y = pos_guess[0] - neighbor_pos[0]
                    rel_x = pos_guess[1] - neighbor_pos[1]
                    grid.loc[i, f"{direction}_y_init_guess"] = rel_y
                    grid.loc[i, f"{direction}_x_init_guess"] = rel_x

    # Parallel translation computation
    if use_parallel and max_workers != 1:
        translation_tasks = []
        
        for direction in ["left", "top"]:
            for i2, g in grid.iterrows():
                i1 = g[direction]
                if pd.isna(i1):
                    continue
                    
                i1 = int(i1)
                image1 = images[i1]
                image2 = images[i2]
                
                if position_initial_guess is not None:
                    def get_lims(dimension, size):
                        val = g[f"{direction}_{dimension}_init_guess"]
                        r = size * overlap_diff_threshold / 100.0
                        return np.round([val - r, val + r]).astype(np.int64)

                    lims = np.array([
                        get_lims(dimension, size)
                        for dimension, size in zip("yx", [sizeY, sizeX])
                    ])
                else:
                    lims = np.array([[-sizeY, sizeY], [-sizeX, sizeX]])
                
                translation_tasks.append((i1, i2, image1, image2, direction, lims, ncc_threshold))
        
        # Execute parallel translation computation
        print(f"Computing {len(translation_tasks)} translations in parallel...")

        # Use ThreadPoolExecutor instead of ProcessPoolExecutor for better compatibility
        with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
            results = list(tqdm(
                executor.map(compute_translation_parallel, translation_tasks),
                total=len(translation_tasks),
                desc="Translation computation"
            ))
        
        # Process results
        for result in results:
            if result['success']:
                i2 = result['i2']
                direction = result['direction']
                grid.loc[i2, f"{direction}_ncc_first"] = result['ncc']
                grid.loc[i2, f"{direction}_y_first"] = result['y']
                grid.loc[i2, f"{direction}_x_first"] = result['x']
            else:
                print(f"Warning: Translation computation failed for image {result['i2']}: {result.get('error', 'Unknown error')}")
    
    else:
        # Sequential processing (original algorithm)
        for direction in ["left", "top"]:
            for i2, g in tqdm(grid.iterrows(), total=len(grid), desc=f"Computing {direction} translations"):
                i1 = g[direction]
                if pd.isna(i1):
                    continue
                    
                i1 = int(i1)
                image1 = images[i1]
                image2 = images[i2]

                PCM = pcm(image1, image2).real
                if position_initial_guess is not None:
                    def get_lims(dimension, size):
                        val = g[f"{direction}_{dimension}_init_guess"]
                        r = size * overlap_diff_threshold / 100.0
                        return np.round([val - r, val + r]).astype(np.int64)

                    lims = np.array([
                        get_lims(dimension, size)
                        for dimension, size in zip("yx", [sizeY, sizeX])
                    ])
                else:
                    lims = np.array([[-sizeY, sizeY], [-sizeX, sizeX]])
                    
                yins, xins, _ = multi_peak_max(PCM)
                max_peak = interpret_translation(
                    image1, image2, yins, xins, *lims[0], *lims[1]
                )
                for j, key in enumerate(["ncc", "y", "x"]):
                    grid.loc[i2, f"{direction}_{key}_first"] = max_peak[j]

    # Continue with rest of algorithm (stage model, refinement, etc.)
    # Check thresholds
    assert np.any(
        grid["top_ncc_first"] > ncc_threshold
    ), "there is no good top pair, (try lowering the ncc_threshold)"
    assert np.any(
        grid["left_ncc_first"] > ncc_threshold
    ), "there is no good left pair, (try lowering the ncc_threshold)"
    
    # Stage model computation
    predictor = ElipticEnvelopPredictor(contamination=0.4, epsilon=0.01, random_seed=0)
    left_displacement = compute_image_overlap2(
        grid[grid["left_ncc_first"] > ncc_threshold], "left", sizeY, sizeX, predictor
    )
    top_displacement = compute_image_overlap2(
        grid[grid["top_ncc_first"] > ncc_threshold], "top", sizeY, sizeX, predictor
    )

    overlap_left = 100 * (1 - left_displacement[1])
    overlap_top = 100 * (1 - top_displacement[0])

    # Continue with filtering and refinement
    for direction, overlap, size in zip(
        ["left", "top"], [overlap_left, overlap_top], [sizeX, sizeY]
    ):
        isvalid = filter_by_overlap_and_correlation(
            grid[f"{direction}_y_first"],
            grid[f"{direction}_ncc_first"],
            overlap,
            size,
            pou,
            ncc_threshold,
        )
        grid[f"{direction}_valid1"] = isvalid

        isvalid = filter_outliers(grid[f"{direction}_y_first"], isvalid)
        grid[f"{direction}_valid2"] = isvalid

    r = max(15, min(sizeY, sizeX) * pou / 100)

    grid = filter_by_repeatability(grid, r, ncc_threshold)
    grid = replace_invalid_translations(grid)

    grid = refine_translations(images, grid, r)

    tree = compute_maximum_spanning_tree(grid)
    grid = compute_final_position(grid, tree)

    prop_dict = {
        "W": sizeY,
        "H": sizeX,
        "overlap_left": overlap_left,
        "overlap_top": overlap_top,
        "repeatability": r,
    }
    
    if row_col_transpose:
        grid = grid.rename(columns={"x_pos": "y_pos", "y_pos": "x_pos"})
        
    if full_output:
        return grid, prop_dict
    else:
        return grid[["row", "col", "y_pos", "x_pos"]], prop_dict
