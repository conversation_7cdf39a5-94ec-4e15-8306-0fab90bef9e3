repos:
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.2.0
    hooks:
      - id: check-yaml
      - id: check-toml
      - id: detect-private-key
      #      - id: name-tests-test
      - id: end-of-file-fixer
      - id: trailing-whitespace
      - id: check-added-large-files
  - repo: https://github.com/psf/black
    rev: 22.3.0
    hooks:
      - id: black
  - repo: https://github.com/PyCQA/flake8
    rev: 4.0.1
    hooks:
      - id: flake8
        types: [python]
        require_serial: true
  - repo: https://github.com/asottile/reorder_python_imports
    rev: v3.0.1
    hooks:
      - id: reorder-python-imports
        name: Reorder python imports
        entry: reorder-python-imports
        types: [python]
        args: [--application-directories=src]
  - repo: https://github.com/pre-commit/mirrors-prettier
    rev: v2.6.2
    hooks:
      - id: prettier
