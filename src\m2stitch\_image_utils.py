"""Image processing utilities for M2Stitch with color support and performance optimizations."""

import numpy as np
from typing import Union, Tuple, Optional
from PIL import Image
import concurrent.futures
from functools import partial

from ._typing_utils import NumArray, Float


def rgb_to_luminance(rgb_image: NumArray) -> NumArray:
    """
    Convert RGB image to luminance using ITU-R BT.709 standard.
    
    Parameters
    ----------
    rgb_image : np.ndarray
        RGB image with shape (height, width, 3)
        
    Returns
    -------
    luminance : np.ndarray
        Luminance image with shape (height, width)
    """
    if rgb_image.ndim != 3 or rgb_image.shape[2] != 3:
        raise ValueError("Input must be RGB image with shape (height, width, 3)")
    
    # ITU-R BT.709 luminance weights
    weights = np.array([0.2126, 0.7152, 0.0722], dtype=np.float32)
    return np.dot(rgb_image.astype(np.float32), weights).astype(rgb_image.dtype)


def load_image_optimized(filepath: str, target_mode: str = 'auto') -> NumArray:
    """
    Load and convert image with optimized memory usage.
    
    Parameters
    ----------
    filepath : str
        Path to image file
    target_mode : str
        Target mode: 'auto', 'L' (grayscale), 'RGB', or 'preserve'
        
    Returns
    -------
    image : np.ndarray
        Loaded image array
    """
    with Image.open(filepath) as img:
        original_mode = img.mode
        
        if target_mode == 'preserve':
            # Keep original format
            return np.array(img)
        elif target_mode == 'auto':
            # Auto-detect best mode
            if original_mode in ['L', 'P']:
                target_mode = 'L'
            else:
                target_mode = 'RGB'
        
        if target_mode == 'L' and original_mode != 'L':
            if original_mode == 'RGB':
                # Use optimized RGB to luminance conversion
                rgb_array = np.array(img)
                return rgb_to_luminance(rgb_array)
            else:
                # Use PIL conversion for other modes
                img = img.convert('L')
        elif target_mode == 'RGB' and original_mode != 'RGB':
            img = img.convert('RGB')
        
        return np.array(img)


def load_images_parallel(filepaths: list, target_mode: str = 'auto', 
                        max_workers: Optional[int] = None) -> NumArray:
    """
    Load multiple images in parallel.
    
    Parameters
    ----------
    filepaths : list
        List of image file paths
    target_mode : str
        Target mode for all images
    max_workers : int, optional
        Maximum number of worker threads
        
    Returns
    -------
    images : np.ndarray
        Array of loaded images with shape (n_images, height, width) or 
        (n_images, height, width, channels)
    """
    if not filepaths:
        raise ValueError("No image files provided")
    
    # Load first image to determine dimensions
    first_img = load_image_optimized(filepaths[0], target_mode)
    
    # Initialize array for all images
    if first_img.ndim == 2:
        images = np.zeros((len(filepaths), first_img.shape[0], first_img.shape[1]), 
                         dtype=first_img.dtype)
    else:
        images = np.zeros((len(filepaths), first_img.shape[0], first_img.shape[1], first_img.shape[2]), 
                         dtype=first_img.dtype)
    
    images[0] = first_img
    
    # Load remaining images in parallel
    if len(filepaths) > 1:
        load_func = partial(load_image_optimized, target_mode=target_mode)
        
        with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
            future_to_idx = {executor.submit(load_func, filepath): idx 
                           for idx, filepath in enumerate(filepaths[1:], 1)}
            
            for future in concurrent.futures.as_completed(future_to_idx):
                idx = future_to_idx[future]
                try:
                    images[idx] = future.result()
                except Exception as exc:
                    raise RuntimeError(f'Image {filepaths[idx]} generated an exception: {exc}')
    
    return images


def prepare_image_for_stitching(image: NumArray) -> NumArray:
    """
    Prepare image for stitching algorithm (convert to grayscale if needed).
    
    Parameters
    ----------
    image : np.ndarray
        Input image (grayscale or color)
        
    Returns
    -------
    processed_image : np.ndarray
        Grayscale image ready for stitching
    """
    if image.ndim == 2:
        # Already grayscale
        return image
    elif image.ndim == 3:
        if image.shape[2] == 3:
            # RGB to luminance
            return rgb_to_luminance(image)
        elif image.shape[2] == 4:
            # RGBA - ignore alpha channel
            return rgb_to_luminance(image[:, :, :3])
        else:
            raise ValueError(f"Unsupported number of channels: {image.shape[2]}")
    else:
        raise ValueError(f"Unsupported image dimensions: {image.ndim}")


def create_color_stitched_image(color_images: NumArray, positions: NumArray, 
                               output_size: Tuple[int, int],
                               blend_mode: str = 'overlay') -> NumArray:
    """
    Create stitched color image from individual color tiles.
    
    Parameters
    ----------
    color_images : np.ndarray
        Array of color images with shape (n_images, height, width, channels)
    positions : np.ndarray
        Array of positions with shape (n_images, 2) containing (y, x) coordinates
    output_size : tuple
        Output image size (height, width)
    blend_mode : str
        Blending mode: 'overlay', 'average', or 'max'
        
    Returns
    -------
    stitched_image : np.ndarray
        Stitched color image
    """
    if color_images.ndim != 4:
        raise ValueError("Color images must have 4 dimensions (n_images, height, width, channels)")
    
    n_images, tile_height, tile_width, n_channels = color_images.shape
    output_height, output_width = output_size
    
    # Initialize output image and weight map
    stitched = np.zeros((output_height, output_width, n_channels), dtype=np.float32)
    weights = np.zeros((output_height, output_width), dtype=np.float32)
    
    for i in range(n_images):
        y_pos, x_pos = int(positions[i, 0]), int(positions[i, 1])
        
        # Calculate valid region
        y_end = min(y_pos + tile_height, output_height)
        x_end = min(x_pos + tile_width, output_width)
        
        if y_pos >= output_height or x_pos >= output_width or y_end <= y_pos or x_end <= x_pos:
            continue
        
        # Extract valid portion of tile
        tile_y_end = y_end - y_pos
        tile_x_end = x_end - x_pos
        tile_region = color_images[i, :tile_y_end, :tile_x_end].astype(np.float32)
        
        if blend_mode == 'overlay':
            # Simple overlay (last image wins)
            stitched[y_pos:y_end, x_pos:x_end] = tile_region
            weights[y_pos:y_end, x_pos:x_end] = 1.0
        elif blend_mode == 'average':
            # Weighted average blending
            current_weights = weights[y_pos:y_end, x_pos:x_end]
            new_weights = current_weights + 1.0
            
            # Update stitched image with weighted average
            for c in range(n_channels):
                current_values = stitched[y_pos:y_end, x_pos:x_end, c]
                stitched[y_pos:y_end, x_pos:x_end, c] = (
                    (current_values * current_weights + tile_region[:, :, c]) / new_weights
                )
            
            weights[y_pos:y_end, x_pos:x_end] = new_weights
        elif blend_mode == 'max':
            # Maximum intensity blending
            current_values = stitched[y_pos:y_end, x_pos:x_end]
            stitched[y_pos:y_end, x_pos:x_end] = np.maximum(current_values, tile_region)
            weights[y_pos:y_end, x_pos:x_end] = 1.0
    
    return stitched.astype(color_images.dtype)


def enhance_image_contrast(image: NumArray, clip_limit: Float = 2.0) -> NumArray:
    """
    Enhance image contrast using adaptive histogram equalization.
    
    Parameters
    ----------
    image : np.ndarray
        Input image
    clip_limit : float
        Clipping limit for contrast enhancement
        
    Returns
    -------
    enhanced_image : np.ndarray
        Contrast-enhanced image
    """
    try:
        import cv2
        if image.ndim == 2:
            clahe = cv2.createCLAHE(clipLimit=clip_limit, tileGridSize=(8, 8))
            return clahe.apply(image)
        else:
            # Apply to each channel separately
            enhanced = np.zeros_like(image)
            clahe = cv2.createCLAHE(clipLimit=clip_limit, tileGridSize=(8, 8))
            for c in range(image.shape[2]):
                enhanced[:, :, c] = clahe.apply(image[:, :, c])
            return enhanced
    except ImportError:
        # Fallback: simple histogram stretching
        if image.ndim == 2:
            return _simple_contrast_stretch(image)
        else:
            enhanced = np.zeros_like(image)
            for c in range(image.shape[2]):
                enhanced[:, :, c] = _simple_contrast_stretch(image[:, :, c])
            return enhanced


def _simple_contrast_stretch(image: NumArray) -> NumArray:
    """Simple contrast stretching fallback."""
    min_val = np.percentile(image, 1)
    max_val = np.percentile(image, 99)
    
    if max_val > min_val:
        stretched = (image.astype(np.float32) - min_val) / (max_val - min_val)
        stretched = np.clip(stretched, 0, 1)
        return (stretched * 255).astype(image.dtype)
    else:
        return image
