#!/usr/bin/env python3
"""
改进的图像拼接系统
解决平移计算和融合问题

主要改进：
1. 精确的特征点匹配替代粗糙估算
2. 改进的图像融合算法
3. 更好的重叠区域处理
4. 渐进式位置优化
"""

import os
import numpy as np
from PIL import Image
import cv2
import time
from tqdm import tqdm
import concurrent.futures
from typing import List, Tuple, Dict
from scipy.optimize import minimize


class ImprovedStitcher:
    """改进的图像拼接器"""
    
    def __init__(self, max_workers=4, jpeg_quality=90):
        """
        初始化
        
        Parameters:
        -----------
        max_workers : int
            最大工作线程数
        jpeg_quality : int
            JPEG压缩质量
        """
        self.max_workers = max_workers
        self.jpeg_quality = jpeg_quality
        Image.MAX_IMAGE_PIXELS = None
        
        # 初始化特征检测器
        self.sift = cv2.SIFT_create(nfeatures=1000)
        self.matcher = cv2.BFMatcher()
        
    def improved_stitch(self, folder_path: str, num_cols: int = 16, num_rows: int = 14,
                       output_path: str = "improved_result.jpg"):
        """
        改进的拼接主函数
        """
        print("=" * 60)
        print("改进的图像拼接系统")
        print("=" * 60)
        
        total_start = time.time()
        
        # 1. 加载图像
        print("1. 加载图像...")
        images, grid_info = self._load_images(folder_path, num_cols, num_rows)
        
        # 2. 精确计算图像间位移
        print("2. 精确计算图像位移...")
        displacements = self._compute_accurate_displacements(images, grid_info, num_cols, num_rows)
        
        # 3. 全局位置优化
        print("3. 全局位置优化...")
        positions = self._optimize_global_positions(displacements, grid_info, images.shape[1:3])
        
        # 4. 高质量融合拼接
        print("4. 高质量融合拼接...")
        self._stitch_with_blending(images, positions, output_path)
        
        total_time = time.time() - total_start
        print(f"\n✅ 总耗时: {total_time:.2f} 秒")
        print(f"📁 输出文件: {output_path}")
        
        # 显示文件大小
        if os.path.exists(output_path):
            file_size = os.path.getsize(output_path) / (1024 * 1024)
            print(f"📊 文件大小: {file_size:.2f} MB")
        
        return positions
    
    def _load_images(self, folder_path: str, num_cols: int, num_rows: int):
        """加载图像"""
        
        # 获取文件列表
        image_files = []
        for i in range(1, num_cols * num_rows + 1):
            filepath = os.path.join(folder_path, f"s_{i:04d}.jpg")
            if os.path.exists(filepath):
                image_files.append(filepath)
        
        print(f"📷 找到 {len(image_files)} 张图像")
        
        # 加载图像
        def load_image(filepath):
            img = cv2.imread(filepath)
            return cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
        
        start_time = time.time()
        first_img = load_image(image_files[0])
        img_height, img_width = first_img.shape[:2]
        
        images = np.zeros((len(image_files), img_height, img_width, 3), dtype=np.uint8)
        images[0] = first_img
        
        # 并行加载
        with concurrent.futures.ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            futures = {executor.submit(load_image, filepath): i 
                      for i, filepath in enumerate(image_files[1:], 1)}
            
            for future in tqdm(concurrent.futures.as_completed(futures), 
                             total=len(futures), desc="⚡ 加载"):
                idx = futures[future]
                images[idx] = future.result()
        
        load_time = time.time() - start_time
        print(f"⏱️  加载耗时: {load_time:.2f} 秒")
        
        # 生成网格信息
        grid_info = []
        for img_idx in range(len(image_files)):
            row = img_idx // num_cols
            if row % 2 == 0:  # 偶数行：从左到右
                col = img_idx % num_cols
            else:  # 奇数行：从右到左
                col = num_cols - 1 - (img_idx % num_cols)
            grid_info.append((row, col, img_idx))
        
        return images, grid_info
    
    def _compute_accurate_displacements(self, images: np.ndarray, grid_info: List[Tuple], 
                                      num_cols: int, num_rows: int) -> Dict:
        """精确计算图像间位移"""
        
        # 创建位置到索引的映射
        pos_to_idx = {(row, col): img_idx for row, col, img_idx in grid_info}
        
        displacements = {}
        
        # 计算所有相邻图像对的位移
        pairs_to_compute = []
        
        # 水平相邻对
        for row in range(num_rows):
            for col in range(num_cols - 1):
                if (row, col) in pos_to_idx and (row, col + 1) in pos_to_idx:
                    idx1 = pos_to_idx[(row, col)]
                    idx2 = pos_to_idx[(row, col + 1)]
                    pairs_to_compute.append((idx1, idx2, 'horizontal', (row, col)))
        
        # 垂直相邻对
        for row in range(num_rows - 1):
            for col in range(num_cols):
                if (row, col) in pos_to_idx and (row + 1, col) in pos_to_idx:
                    idx1 = pos_to_idx[(row, col)]
                    idx2 = pos_to_idx[(row + 1, col)]
                    pairs_to_compute.append((idx1, idx2, 'vertical', (row, col)))
        
        print(f"🔍 计算 {len(pairs_to_compute)} 个图像对的位移...")
        
        # 并行计算位移
        def compute_displacement(args):
            idx1, idx2, direction, pos = args
            return self._compute_single_displacement(images[idx1], images[idx2], direction), (idx1, idx2, direction, pos)
        
        with concurrent.futures.ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            futures = [executor.submit(compute_displacement, args) for args in pairs_to_compute]
            
            for future in tqdm(concurrent.futures.as_completed(futures), 
                             total=len(futures), desc="📐 计算位移"):
                displacement, (idx1, idx2, direction, pos) = future.result()
                displacements[(idx1, idx2)] = {
                    'displacement': displacement,
                    'direction': direction,
                    'position': pos,
                    'confidence': displacement[2] if len(displacement) > 2 else 1.0
                }
        
        return displacements
    
    def _compute_single_displacement(self, img1: np.ndarray, img2: np.ndarray, direction: str) -> Tuple:
        """计算单个图像对的位移"""
        
        # 转换为灰度
        gray1 = cv2.cvtColor(img1, cv2.COLOR_RGB2GRAY)
        gray2 = cv2.cvtColor(img2, cv2.COLOR_RGB2GRAY)
        
        # 方法1：特征点匹配
        displacement_features = self._feature_based_alignment(gray1, gray2, direction)
        
        # 方法2：模板匹配（作为备选）
        displacement_template = self._template_matching(gray1, gray2, direction)
        
        # 方法3：相位相关（最精确但较慢）
        displacement_phase = self._phase_correlation_precise(gray1, gray2)
        
        # 选择最可靠的结果
        candidates = [
            (displacement_features, 'features'),
            (displacement_template, 'template'),
            (displacement_phase, 'phase')
        ]
        
        # 选择置信度最高的结果
        best_displacement = max(candidates, key=lambda x: x[0][2] if len(x[0]) > 2 else 0)
        
        return best_displacement[0]
    
    def _feature_based_alignment(self, img1: np.ndarray, img2: np.ndarray, direction: str) -> Tuple:
        """基于特征点的对齐"""
        
        try:
            # 检测特征点
            kp1, des1 = self.sift.detectAndCompute(img1, None)
            kp2, des2 = self.sift.detectAndCompute(img2, None)
            
            if des1 is None or des2 is None or len(des1) < 10 or len(des2) < 10:
                return (0, 0, 0.0)
            
            # 匹配特征点
            matches = self.matcher.knnMatch(des1, des2, k=2)
            
            # 应用比率测试
            good_matches = []
            for match_pair in matches:
                if len(match_pair) == 2:
                    m, n = match_pair
                    if m.distance < 0.7 * n.distance:
                        good_matches.append(m)
            
            if len(good_matches) < 10:
                return (0, 0, 0.0)
            
            # 提取匹配点
            src_pts = np.float32([kp1[m.queryIdx].pt for m in good_matches]).reshape(-1, 1, 2)
            dst_pts = np.float32([kp2[m.trainIdx].pt for m in good_matches]).reshape(-1, 1, 2)
            
            # 计算变换矩阵
            M, mask = cv2.findHomography(src_pts, dst_pts, cv2.RANSAC, 5.0)
            
            if M is not None:
                # 提取平移量
                dx = M[0, 2]
                dy = M[1, 2]
                confidence = np.sum(mask) / len(mask)
                
                return (int(dy), int(dx), confidence)
            
        except Exception as e:
            print(f"特征匹配失败: {e}")
        
        return (0, 0, 0.0)
    
    def _template_matching(self, img1: np.ndarray, img2: np.ndarray, direction: str) -> Tuple:
        """改进的模板匹配"""
        
        h, w = img1.shape
        
        if direction == 'horizontal':
            # 水平匹配：使用右边缘作为模板
            template_width = min(w // 4, 200)
            template = img1[:, -template_width:]
            search_width = min(w // 2, 400)
            search_area = img2[:, :search_width]
        else:  # vertical
            # 垂直匹配：使用下边缘作为模板
            template_height = min(h // 4, 200)
            template = img1[-template_height:, :]
            search_height = min(h // 2, 400)
            search_area = img2[:search_height, :]
        
        if template.size == 0 or search_area.size == 0:
            return (0, 0, 0.0)
        
        # 模板匹配
        result = cv2.matchTemplate(search_area, template, cv2.TM_CCOEFF_NORMED)
        _, max_val, _, max_loc = cv2.minMaxLoc(result)
        
        if direction == 'horizontal':
            dy = 0
            dx = max_loc[0] - template_width
        else:
            dy = max_loc[1] - template_height
            dx = 0
        
        return (dy, dx, max_val)
    
    def _phase_correlation_precise(self, img1: np.ndarray, img2: np.ndarray) -> Tuple:
        """精确的相位相关"""
        
        # 预处理：应用窗函数减少边缘效应
        h, w = img1.shape
        window = np.outer(np.hanning(h), np.hanning(w))
        
        img1_windowed = img1.astype(np.float32) * window
        img2_windowed = img2.astype(np.float32) * window
        
        # FFT
        F1 = np.fft.fft2(img1_windowed)
        F2 = np.fft.fft2(img2_windowed)
        
        # 相位相关
        cross_power = F1 * np.conjugate(F2)
        magnitude = np.abs(cross_power)
        magnitude[magnitude == 0] = 1e-10
        
        correlation = np.fft.ifft2(cross_power / magnitude).real
        
        # 找到峰值
        peak_y, peak_x = np.unravel_index(np.argmax(correlation), correlation.shape)
        
        # 转换为位移
        if peak_y > h // 2:
            peak_y -= h
        if peak_x > w // 2:
            peak_x -= w
        
        # 计算置信度
        peak_value = correlation[peak_y % h, peak_x % w]
        mean_value = np.mean(correlation)
        confidence = (peak_value - mean_value) / (np.max(correlation) - mean_value)
        
        return (peak_y, peak_x, confidence)
    
    def _optimize_global_positions(self, displacements: Dict, grid_info: List[Tuple], 
                                 image_shape: Tuple) -> np.ndarray:
        """全局位置优化"""
        
        n_images = len(grid_info)
        
        # 初始位置估计
        img_height, img_width = image_shape
        overlap_ratio = 0.1
        step_x = int(img_width * (1 - overlap_ratio))
        step_y = int(img_height * (1 - overlap_ratio))
        
        initial_positions = np.zeros((n_images, 2))
        for i, (row, col, img_idx) in enumerate(grid_info):
            initial_positions[img_idx] = [row * step_y, col * step_x]
        
        # 构建约束方程
        def objective(positions_flat):
            positions = positions_flat.reshape(n_images, 2)
            error = 0
            
            for (idx1, idx2), info in displacements.items():
                displacement = info['displacement']
                confidence = info['confidence']
                
                if confidence > 0.3:  # 只使用高置信度的位移
                    predicted_pos2 = positions[idx1] + np.array([displacement[0], displacement[1]])
                    actual_pos2 = positions[idx2]
                    
                    # 加权误差
                    error += confidence * np.sum((predicted_pos2 - actual_pos2) ** 2)
            
            return error
        
        # 优化
        print("🔧 执行全局位置优化...")
        result = minimize(objective, initial_positions.flatten(), method='L-BFGS-B')
        
        optimized_positions = result.x.reshape(n_images, 2)
        
        # 调整为正坐标
        min_y, min_x = np.min(optimized_positions, axis=0)
        optimized_positions[:, 0] -= min_y
        optimized_positions[:, 1] -= min_x
        
        return optimized_positions.astype(int)
    
    def _stitch_with_blending(self, images: np.ndarray, positions: np.ndarray, output_path: str):
        """高质量融合拼接"""
        
        img_height, img_width = images.shape[1], images.shape[2]
        
        # 计算输出尺寸
        max_y = positions[:, 0].max() + img_height
        max_x = positions[:, 1].max() + img_width
        
        print(f"📐 输出尺寸: {max_x} x {max_y}")
        
        # 创建输出图像和权重图
        output = np.zeros((max_y, max_x, 3), dtype=np.float32)
        weights = np.zeros((max_y, max_x), dtype=np.float32)
        
        # 创建渐变权重模板
        weight_template = self._create_weight_template(img_height, img_width)
        
        # 融合图像
        for i, (y_pos, x_pos) in enumerate(tqdm(positions, desc="🎨 融合")):
            y_end = min(y_pos + img_height, max_y)
            x_end = min(x_pos + img_width, max_x)
            
            # 当前图像区域
            img_region = images[i][:y_end-y_pos, :x_end-x_pos].astype(np.float32)
            weight_region = weight_template[:y_end-y_pos, :x_end-x_pos]
            
            # 累积加权图像
            for c in range(3):
                output[y_pos:y_end, x_pos:x_end, c] += img_region[:, :, c] * weight_region
            
            weights[y_pos:y_end, x_pos:x_end] += weight_region
        
        # 归一化
        for c in range(3):
            mask = weights > 0
            output[mask, c] /= weights[mask]
        
        # 转换为uint8并保存
        output = np.clip(output, 0, 255).astype(np.uint8)
        
        # 保存
        pil_image = Image.fromarray(output, 'RGB')
        pil_image.save(output_path, 'JPEG', quality=self.jpeg_quality, optimize=True)
        
        # 创建缩略图
        thumbnail_path = output_path.replace('.jpg', '_thumb.jpg')
        pil_image.thumbnail((1500, 1500), Image.Resampling.LANCZOS)
        pil_image.save(thumbnail_path, 'JPEG', quality=80, optimize=True)
        
        print(f"✅ 已保存: {output_path}")
        print(f"🖼️  缩略图: {thumbnail_path}")
    
    def _create_weight_template(self, height: int, width: int) -> np.ndarray:
        """创建渐变权重模板"""
        
        # 创建距离变换
        mask = np.ones((height, width), dtype=np.uint8)
        dist = cv2.distanceTransform(mask, cv2.DIST_L2, 5)
        
        # 归一化到0-1
        dist = dist / np.max(dist)
        
        # 应用平滑函数
        weight = np.power(dist, 0.5)  # 平方根函数使边缘更平滑
        
        return weight


def main():
    """主函数"""
    folder_path = r"D:\images\image_55"
    
    if not os.path.exists(folder_path):
        print(f"❌ 错误: 文件夹 {folder_path} 不存在")
        return
    
    # 创建改进的拼接器
    stitcher = ImprovedStitcher(max_workers=6, jpeg_quality=90)
    
    # 执行改进的拼接
    positions = stitcher.improved_stitch(
        folder_path=folder_path,
        num_cols=16,
        num_rows=14,
        output_path="improved_result.jpg"
    )
    
    print("🎉 改进拼接完成！")


if __name__ == "__main__":
    main()
