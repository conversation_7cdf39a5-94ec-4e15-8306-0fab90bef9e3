#!/usr/bin/env python3
"""
完整的图像拼接系统 - 单文件版本
支持彩色图像、性能优化、Python 3.13兼容

主要功能：
1. 图像加载和预处理
2. 相位相关法计算图像位移
3. 全局优化和位置计算
4. 彩色图像拼接输出

作者：基于M2Stitch算法优化
"""

import os
import numpy as np
import pandas as pd
from PIL import Image
import time
from tqdm import tqdm
import warnings
from typing import List, Tuple, Optional, Union
import concurrent.futures
from functools import partial
import multiprocessing


class ImageStitcher:
    """完整的图像拼接类"""
    
    def __init__(self, ncc_threshold=0.3, overlap_uncertainty=5, max_workers=None):
        """
        初始化拼接器
        
        Parameters:
        -----------
        ncc_threshold : float
            归一化互相关阈值，默认0.3
        overlap_uncertainty : float
            重叠不确定性百分比，默认5
        max_workers : int
            最大并行工作线程数，默认为CPU核心数
        """
        self.ncc_threshold = ncc_threshold
        self.overlap_uncertainty = overlap_uncertainty
        self.max_workers = max_workers or min(8, multiprocessing.cpu_count())
        
        # 增加PIL图像大小限制
        Image.MAX_IMAGE_PIXELS = None
        
    def load_images_from_folder(self, folder_path: str, num_cols: int, num_rows: int, 
                               preserve_color: bool = True) -> Tuple[np.ndarray, List[int], List[int]]:
        """
        从文件夹加载图像并生成Snake模式的行列索引
        
        Parameters:
        -----------
        folder_path : str
            图像文件夹路径
        num_cols : int
            列数
        num_rows : int
            行数
        preserve_color : bool
            是否保持彩色，默认True
            
        Returns:
        --------
        images : np.ndarray
            图像数组
        rows : List[int]
            行索引列表
        cols : List[int]
            列索引列表
        """
        print("正在加载图像...")
        
        # 获取所有图像文件
        image_files = []
        for i in range(1, num_cols * num_rows + 1):
            filename = f"s_{i:04d}.jpg"
            filepath = os.path.join(folder_path, filename)
            if os.path.exists(filepath):
                image_files.append(filepath)
            else:
                print(f"警告: 文件 {filename} 不存在")
        
        print(f"找到 {len(image_files)} 张图像")
        
        # 并行加载图像
        start_time = time.time()
        images = self._load_images_parallel(image_files, preserve_color)
        load_time = time.time() - start_time
        
        print(f"图像加载完成，耗时: {load_time:.2f} 秒")
        print(f"图像尺寸: {images.shape}")
        print(f"内存占用: {images.nbytes / 1024 / 1024:.2f} MB")
        
        # 生成Snake模式的行列索引
        rows, cols = self._generate_snake_indices(len(image_files), num_cols)
        
        return images, rows, cols
    
    def _load_images_parallel(self, filepaths: List[str], preserve_color: bool) -> np.ndarray:
        """并行加载图像"""
        def load_single_image(filepath):
            with Image.open(filepath) as img:
                if preserve_color and img.mode != 'RGB':
                    img = img.convert('RGB')
                elif not preserve_color and img.mode != 'L':
                    if img.mode == 'RGB':
                        # 使用ITU-R BT.709标准转换为灰度
                        rgb_array = np.array(img)
                        weights = np.array([0.2126, 0.7152, 0.0722])
                        gray_array = np.dot(rgb_array, weights).astype(np.uint8)
                        return gray_array
                    else:
                        img = img.convert('L')
                return np.array(img)
        
        # 加载第一张图像确定尺寸
        first_img = load_single_image(filepaths[0])
        
        # 初始化数组
        if preserve_color and first_img.ndim == 3:
            images = np.zeros((len(filepaths), first_img.shape[0], first_img.shape[1], 3), dtype=np.uint8)
        else:
            images = np.zeros((len(filepaths), first_img.shape[0], first_img.shape[1]), dtype=np.uint8)
        
        images[0] = first_img
        
        # 并行加载其余图像
        if len(filepaths) > 1:
            with concurrent.futures.ThreadPoolExecutor(max_workers=self.max_workers) as executor:
                future_to_idx = {executor.submit(load_single_image, filepath): idx 
                               for idx, filepath in enumerate(filepaths[1:], 1)}
                
                for future in tqdm(concurrent.futures.as_completed(future_to_idx), 
                                 total=len(filepaths)-1, desc="加载图像"):
                    idx = future_to_idx[future]
                    images[idx] = future.result()
        
        return images
    
    def _generate_snake_indices(self, num_images: int, num_cols: int) -> Tuple[List[int], List[int]]:
        """生成Snake模式的行列索引"""
        rows, cols = [], []
        
        for img_idx in range(num_images):
            row = img_idx // num_cols
            if row % 2 == 0:  # 偶数行：从左到右
                col = img_idx % num_cols
            else:  # 奇数行：从右到左
                col = num_cols - 1 - (img_idx % num_cols)
            
            rows.append(row)
            cols.append(col)
        
        return rows, cols
    
    def _prepare_images_for_stitching(self, images: np.ndarray) -> np.ndarray:
        """准备用于拼接的图像（转换为灰度）"""
        if images.ndim == 3:  # 已经是灰度
            return images
        elif images.ndim == 4:  # 彩色图像，转换为灰度
            print("将彩色图像转换为灰度用于拼接计算...")
            gray_images = np.zeros((images.shape[0], images.shape[1], images.shape[2]), dtype=np.uint8)
            weights = np.array([0.2126, 0.7152, 0.0722])
            
            for i in tqdm(range(images.shape[0]), desc="转换灰度"):
                gray_images[i] = np.dot(images[i], weights).astype(np.uint8)
            
            return gray_images
        else:
            raise ValueError(f"不支持的图像维度: {images.ndim}")
    
    def _phase_correlation(self, image1: np.ndarray, image2: np.ndarray) -> np.ndarray:
        """计算两幅图像的相位相关矩阵"""
        F1 = np.fft.fft2(image1.astype(np.float32))
        F2 = np.fft.fft2(image2.astype(np.float32))
        FC = F1 * np.conjugate(F2)
        
        # 避免除零
        magnitude = np.abs(FC)
        magnitude[magnitude == 0] = 1e-10
        
        return np.fft.ifft2(FC / magnitude).real
    
    def _find_peak_translation(self, pcm_result: np.ndarray, 
                              image1: np.ndarray, image2: np.ndarray) -> Tuple[float, int, int]:
        """从相位相关结果中找到最佳平移"""
        # 找到峰值位置
        peak_y, peak_x = np.unravel_index(np.argmax(pcm_result), pcm_result.shape)
        
        # 转换为平移量
        height, width = pcm_result.shape
        if peak_y > height // 2:
            peak_y -= height
        if peak_x > width // 2:
            peak_x -= width
        
        # 计算归一化互相关
        ncc_value = self._compute_ncc_at_offset(image1, image2, peak_y, peak_x)
        
        return ncc_value, peak_y, peak_x
    
    def _compute_ncc_at_offset(self, image1: np.ndarray, image2: np.ndarray, 
                              offset_y: int, offset_x: int) -> float:
        """计算指定偏移处的归一化互相关"""
        h, w = image1.shape
        
        # 计算重叠区域
        y1_start = max(0, offset_y)
        y1_end = min(h, h + offset_y)
        x1_start = max(0, offset_x)
        x1_end = min(w, w + offset_x)
        
        y2_start = max(0, -offset_y)
        y2_end = min(h, h - offset_y)
        x2_start = max(0, -offset_x)
        x2_end = min(w, w - offset_x)
        
        if y1_end <= y1_start or x1_end <= x1_start:
            return 0.0
        
        region1 = image1[y1_start:y1_end, x1_start:x1_end].astype(np.float32)
        region2 = image2[y2_start:y2_end, x2_start:x2_end].astype(np.float32)
        
        if region1.size == 0 or region2.size == 0:
            return 0.0
        
        # 计算归一化互相关
        region1_flat = region1.flatten()
        region2_flat = region2.flatten()
        
        mean1, mean2 = np.mean(region1_flat), np.mean(region2_flat)
        std1, std2 = np.std(region1_flat), np.std(region2_flat)
        
        if std1 == 0 or std2 == 0:
            return 0.0
        
        correlation = np.mean((region1_flat - mean1) * (region2_flat - mean2)) / (std1 * std2)
        return max(-1.0, min(1.0, correlation))  # 限制在[-1, 1]范围内
    
    def stitch_images(self, images: np.ndarray, rows: List[int], cols: List[int]) -> Tuple[pd.DataFrame, dict]:
        """
        执行图像拼接算法
        
        Parameters:
        -----------
        images : np.ndarray
            图像数组
        rows : List[int]
            行索引
        cols : List[int]
            列索引
            
        Returns:
        --------
        result_df : pd.DataFrame
            包含位置信息的结果数据框
        info_dict : dict
            拼接信息字典
        """
        print("开始执行拼接算法...")
        start_time = time.time()
        
        # 准备用于拼接的灰度图像
        gray_images = self._prepare_images_for_stitching(images)
        
        # 创建网格数据框
        grid = pd.DataFrame({
            'row': rows,
            'col': cols,
            'image_idx': range(len(rows))
        })
        
        # 建立邻居关系
        self._build_neighbor_relationships(grid)
        
        # 计算平移
        self._compute_translations(grid, gray_images)
        
        # 估计重叠参数
        overlap_info = self._estimate_overlaps(grid)
        
        # 过滤和优化平移
        self._filter_translations(grid, overlap_info)
        
        # 计算最终位置
        final_positions = self._compute_final_positions(grid)
        
        stitch_time = time.time() - start_time
        print(f"拼接算法完成，耗时: {stitch_time:.2f} 秒")
        
        # 准备结果
        result_df = pd.DataFrame({
            'row': rows,
            'col': cols,
            'y_pos': final_positions[:, 0],
            'x_pos': final_positions[:, 1]
        })
        
        info_dict = {
            'stitch_time': stitch_time,
            'overlap_info': overlap_info,
            'num_images': len(images),
            'image_shape': gray_images.shape[1:]
        }
        
        return result_df, info_dict

    def _build_neighbor_relationships(self, grid: pd.DataFrame):
        """建立邻居关系"""
        # 创建位置到索引的映射
        pos_to_idx = {}
        for idx, row in grid.iterrows():
            pos_to_idx[(row['row'], row['col'])] = idx

        # 添加邻居信息
        grid['left_neighbor'] = np.nan
        grid['top_neighbor'] = np.nan

        for idx, row in grid.iterrows():
            r, c = row['row'], row['col']

            # 左邻居
            if (r, c-1) in pos_to_idx:
                grid.loc[idx, 'left_neighbor'] = pos_to_idx[(r, c-1)]

            # 上邻居
            if (r-1, c) in pos_to_idx:
                grid.loc[idx, 'top_neighbor'] = pos_to_idx[(r-1, c)]

    def _compute_translations(self, grid: pd.DataFrame, images: np.ndarray):
        """计算图像间的平移"""
        print("计算图像平移...")

        # 初始化平移结果
        for direction in ['left', 'top']:
            grid[f'{direction}_ncc'] = 0.0
            grid[f'{direction}_dy'] = 0
            grid[f'{direction}_dx'] = 0
            grid[f'{direction}_valid'] = False

        # 计算平移
        total_pairs = 0
        for direction in ['left', 'top']:
            neighbor_col = f'{direction}_neighbor'
            valid_pairs = grid[~grid[neighbor_col].isna()]
            total_pairs += len(valid_pairs)

        with tqdm(total=total_pairs, desc="计算平移") as pbar:
            for direction in ['left', 'top']:
                neighbor_col = f'{direction}_neighbor'

                for idx, row in grid.iterrows():
                    neighbor_idx = row[neighbor_col]
                    if pd.isna(neighbor_idx):
                        continue

                    neighbor_idx = int(neighbor_idx)
                    image1 = images[neighbor_idx]  # 邻居图像
                    image2 = images[row['image_idx']]  # 当前图像

                    # 计算相位相关
                    pcm_result = self._phase_correlation(image1, image2)
                    ncc, dy, dx = self._find_peak_translation(pcm_result, image1, image2)

                    # 存储结果
                    grid.loc[idx, f'{direction}_ncc'] = ncc
                    grid.loc[idx, f'{direction}_dy'] = dy
                    grid.loc[idx, f'{direction}_dx'] = dx
                    grid.loc[idx, f'{direction}_valid'] = ncc > self.ncc_threshold

                    pbar.update(1)

    def _estimate_overlaps(self, grid: pd.DataFrame) -> dict:
        """估计重叠参数"""
        overlap_info = {}

        for direction in ['left', 'top']:
            valid_data = grid[grid[f'{direction}_valid']]
            if len(valid_data) > 0:
                # 使用中位数估计重叠
                dy_median = valid_data[f'{direction}_dy'].median()
                dx_median = valid_data[f'{direction}_dx'].median()

                overlap_info[f'{direction}_overlap_y'] = dy_median
                overlap_info[f'{direction}_overlap_x'] = dx_median
                overlap_info[f'{direction}_count'] = len(valid_data)
            else:
                overlap_info[f'{direction}_overlap_y'] = 0
                overlap_info[f'{direction}_overlap_x'] = 0
                overlap_info[f'{direction}_count'] = 0

        return overlap_info

    def _filter_translations(self, grid: pd.DataFrame, overlap_info: dict):
        """过滤和优化平移结果"""
        print("过滤平移结果...")

        for direction in ['left', 'top']:
            # 基于重叠一致性过滤
            expected_dy = overlap_info[f'{direction}_overlap_y']
            expected_dx = overlap_info[f'{direction}_overlap_x']

            # 计算偏差阈值
            threshold = max(20, min(100, self.overlap_uncertainty))

            for idx, row in grid.iterrows():
                if grid.loc[idx, f'{direction}_valid']:
                    dy = grid.loc[idx, f'{direction}_dy']
                    dx = grid.loc[idx, f'{direction}_dx']

                    # 检查是否在预期范围内
                    dy_diff = abs(dy - expected_dy)
                    dx_diff = abs(dx - expected_dx)

                    if dy_diff > threshold or dx_diff > threshold:
                        grid.loc[idx, f'{direction}_valid'] = False

    def _compute_final_positions(self, grid: pd.DataFrame) -> np.ndarray:
        """计算最终的图像位置"""
        print("计算最终位置...")

        n_images = len(grid)
        positions = np.zeros((n_images, 2))  # [y, x]
        visited = np.zeros(n_images, dtype=bool)

        # 从第一张图像开始
        start_idx = 0
        positions[start_idx] = [0, 0]
        visited[start_idx] = True

        # 使用广度优先搜索传播位置
        queue = [start_idx]

        while queue:
            current_idx = queue.pop(0)
            current_row = grid.iloc[current_idx]

            # 处理右邻居（当前图像是左邻居）
            for idx, row in grid.iterrows():
                if visited[idx]:
                    continue

                # 检查是否是右邻居
                if (not pd.isna(row['left_neighbor']) and
                    int(row['left_neighbor']) == current_idx and
                    row['left_valid']):

                    # 计算位置
                    dy = row['left_dy']
                    dx = row['left_dx']
                    positions[idx] = positions[current_idx] + [dy, dx]
                    visited[idx] = True
                    queue.append(idx)

                # 检查是否是下邻居
                if (not pd.isna(row['top_neighbor']) and
                    int(row['top_neighbor']) == current_idx and
                    row['top_valid']):

                    # 计算位置
                    dy = row['top_dy']
                    dx = row['top_dx']
                    positions[idx] = positions[current_idx] + [dy, dx]
                    visited[idx] = True
                    queue.append(idx)

        # 处理未访问的图像（使用网格位置估计）
        for idx in range(n_images):
            if not visited[idx]:
                row_data = grid.iloc[idx]
                # 使用网格位置的粗略估计
                estimated_y = row_data['row'] * 1800  # 假设图像高度约1800像素
                estimated_x = row_data['col'] * 2200  # 假设图像宽度约2200像素
                positions[idx] = [estimated_y, estimated_x]

        # 调整位置使所有坐标为正
        min_y, min_x = np.min(positions, axis=0)
        positions[:, 0] -= min_y
        positions[:, 1] -= min_x

        return positions.astype(int)

    def create_stitched_image(self, images: np.ndarray, result_df: pd.DataFrame,
                            output_path: str = "stitched_result.png") -> np.ndarray:
        """
        创建最终的拼接图像

        Parameters:
        -----------
        images : np.ndarray
            原始图像数组
        result_df : pd.DataFrame
            包含位置信息的结果数据框
        output_path : str
            输出文件路径

        Returns:
        --------
        stitched_image : np.ndarray
            拼接后的图像
        """
        print("创建拼接图像...")

        # 获取图像尺寸
        if images.ndim == 4:  # 彩色图像
            n_images, img_height, img_width, n_channels = images.shape
            is_color = True
        else:  # 灰度图像
            n_images, img_height, img_width = images.shape
            n_channels = 1
            is_color = False

        # 计算拼接图像尺寸
        max_y = result_df['y_pos'].max() + img_height
        max_x = result_df['x_pos'].max() + img_width

        print(f"拼接图像尺寸: {max_x} x {max_y}")
        print(f"预估文件大小: {max_x * max_y * n_channels / 1024 / 1024:.2f} MB")

        # 创建拼接图像
        if is_color:
            stitched = np.zeros((max_y, max_x, n_channels), dtype=np.uint8)
        else:
            stitched = np.zeros((max_y, max_x), dtype=np.uint8)

        # 放置每张图像
        for idx, row in tqdm(result_df.iterrows(), total=len(result_df), desc="拼接图像"):
            y_pos = int(row['y_pos'])
            x_pos = int(row['x_pos'])

            y_end = min(y_pos + img_height, max_y)
            x_end = min(x_pos + img_width, max_x)

            if is_color:
                stitched[y_pos:y_end, x_pos:x_end] = images[idx][:y_end-y_pos, :x_end-x_pos]
            else:
                stitched[y_pos:y_end, x_pos:x_end] = images[idx][:y_end-y_pos, :x_end-x_pos]

        # 保存图像
        if is_color:
            result_image = Image.fromarray(stitched, 'RGB')
        else:
            result_image = Image.fromarray(stitched, 'L')

        result_image.save(output_path)
        print(f"拼接图像已保存到: {output_path}")

        # 创建缩略图
        thumbnail_size = (2000, 2000)
        result_image.thumbnail(thumbnail_size, Image.Resampling.LANCZOS)
        thumbnail_path = output_path.replace('.png', '_thumbnail.png')
        result_image.save(thumbnail_path)
        print(f"缩略图已保存到: {thumbnail_path}")

        return stitched


def main():
    """主函数 - 演示如何使用ImageStitcher"""

    # 配置参数
    folder_path = r"D:\images\image_55"  # 图像文件夹路径
    num_cols = 16  # 列数
    num_rows = 14  # 行数
    preserve_color = True  # 是否保持彩色

    # 检查文件夹是否存在
    if not os.path.exists(folder_path):
        print(f"错误: 文件夹 {folder_path} 不存在")
        return

    print("=" * 70)
    print("完整图像拼接系统")
    print("=" * 70)

    # 创建拼接器
    stitcher = ImageStitcher(
        ncc_threshold=0.3,      # 降低阈值以适应更多图像
        overlap_uncertainty=10,  # 增加重叠容忍度
        max_workers=8           # 限制线程数避免过载
    )

    try:
        # 1. 加载图像
        images, rows, cols = stitcher.load_images_from_folder(
            folder_path, num_cols, num_rows, preserve_color
        )

        # 2. 执行拼接
        result_df, info_dict = stitcher.stitch_images(images, rows, cols)

        # 3. 显示结果
        print(f"\n拼接结果:")
        print(f"成功处理图像数量: {len(result_df)}")
        print(f"拼接耗时: {info_dict['stitch_time']:.2f} 秒")
        print(f"重叠信息: {info_dict['overlap_info']}")

        # 显示前几个位置
        print(f"\n前10张图像的位置:")
        print(result_df[['row', 'col', 'y_pos', 'x_pos']].head(10))

        # 4. 创建拼接图像
        output_path = "complete_stitched_result.png"
        stitched_image = stitcher.create_stitched_image(images, result_df, output_path)

        print(f"\n拼接完成！")
        print(f"输出文件: {output_path}")
        print(f"缩略图: {output_path.replace('.png', '_thumbnail.png')}")

    except Exception as e:
        print(f"拼接过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
