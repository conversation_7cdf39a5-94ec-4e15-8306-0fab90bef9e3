#!/usr/bin/env python3
"""
简化高效的图像拼接系统
专注于核心功能和性能优化

主要特点：
1. 快速图像加载
2. 高效的相位相关算法
3. 智能位置计算
4. 支持彩色图像输出
5. 内存优化
"""

import os
import numpy as np
import pandas as pd
from PIL import Image
import time
from tqdm import tqdm
from typing import List, Tuple
import concurrent.futures


class FastImageStitcher:
    """快速图像拼接器"""
    
    def __init__(self, ncc_threshold=0.2, max_workers=4):
        """
        初始化
        
        Parameters:
        -----------
        ncc_threshold : float
            归一化互相关阈值，默认0.2（降低以提高成功率）
        max_workers : int
            最大工作线程数，默认4（避免过载）
        """
        self.ncc_threshold = ncc_threshold
        self.max_workers = max_workers
        Image.MAX_IMAGE_PIXELS = None
        
    def quick_stitch(self, folder_path: str, num_cols: int = 16, num_rows: int = 14, 
                    preserve_color: bool = True, output_path: str = "quick_stitched.png"):
        """
        快速拼接主函数
        
        Parameters:
        -----------
        folder_path : str
            图像文件夹路径
        num_cols : int
            列数
        num_rows : int
            行数
        preserve_color : bool
            是否保持彩色
        output_path : str
            输出文件路径
        """
        print("=" * 60)
        print("快速图像拼接系统")
        print("=" * 60)
        
        total_start = time.time()
        
        # 1. 快速加载图像
        print("1. 加载图像...")
        images, positions = self._load_images_fast(folder_path, num_cols, num_rows, preserve_color)
        
        # 2. 计算关键拼接点
        print("2. 计算拼接位置...")
        final_positions = self._compute_positions_smart(images, positions, num_cols, num_rows)
        
        # 3. 创建拼接图像
        print("3. 生成拼接图像...")
        self._create_stitched_image_fast(images, final_positions, output_path)
        
        total_time = time.time() - total_start
        print(f"\n总耗时: {total_time:.2f} 秒")
        print(f"输出文件: {output_path}")
        
        return final_positions
    
    def _load_images_fast(self, folder_path: str, num_cols: int, num_rows: int, 
                         preserve_color: bool) -> Tuple[np.ndarray, List[Tuple[int, int]]]:
        """快速加载图像"""
        
        # 获取文件列表
        image_files = []
        for i in range(1, num_cols * num_rows + 1):
            filepath = os.path.join(folder_path, f"s_{i:04d}.jpg")
            if os.path.exists(filepath):
                image_files.append(filepath)
        
        print(f"找到 {len(image_files)} 张图像")
        
        # 并行加载
        def load_single(filepath):
            with Image.open(filepath) as img:
                if preserve_color and img.mode != 'RGB':
                    img = img.convert('RGB')
                elif not preserve_color and img.mode != 'L':
                    img = img.convert('L')
                return np.array(img)
        
        start_time = time.time()
        
        # 先加载第一张确定尺寸
        first_img = load_single(image_files[0])
        
        # 初始化数组
        if preserve_color and first_img.ndim == 3:
            images = np.zeros((len(image_files), first_img.shape[0], first_img.shape[1], 3), dtype=np.uint8)
        else:
            images = np.zeros((len(image_files), first_img.shape[0], first_img.shape[1]), dtype=np.uint8)
        
        images[0] = first_img
        
        # 并行加载其余图像
        with concurrent.futures.ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            futures = {executor.submit(load_single, filepath): i 
                      for i, filepath in enumerate(image_files[1:], 1)}
            
            for future in tqdm(concurrent.futures.as_completed(futures), 
                             total=len(futures), desc="加载图像"):
                idx = futures[future]
                images[idx] = future.result()
        
        load_time = time.time() - start_time
        print(f"加载完成，耗时: {load_time:.2f} 秒")
        print(f"图像尺寸: {images.shape}")
        print(f"内存占用: {images.nbytes / 1024 / 1024:.2f} MB")
        
        # 生成Snake模式位置
        positions = []
        for img_idx in range(len(image_files)):
            row = img_idx // num_cols
            if row % 2 == 0:  # 偶数行：从左到右
                col = img_idx % num_cols
            else:  # 奇数行：从右到左
                col = num_cols - 1 - (img_idx % num_cols)
            positions.append((row, col))
        
        return images, positions
    
    def _compute_positions_smart(self, images: np.ndarray, grid_positions: List[Tuple[int, int]], 
                               num_cols: int, num_rows: int) -> np.ndarray:
        """智能计算拼接位置"""
        
        # 转换为灰度用于计算（如果需要）
        if images.ndim == 4:
            print("转换为灰度用于位置计算...")
            gray_images = np.zeros((images.shape[0], images.shape[1], images.shape[2]), dtype=np.uint8)
            for i in tqdm(range(images.shape[0]), desc="转换灰度"):
                if images.ndim == 4:
                    # RGB转灰度
                    gray_images[i] = np.dot(images[i], [0.299, 0.587, 0.114]).astype(np.uint8)
                else:
                    gray_images[i] = images[i]
        else:
            gray_images = images
        
        img_height, img_width = gray_images.shape[1], gray_images.shape[2]
        
        # 估算重叠（基于10%重叠假设）
        overlap_ratio = 0.1
        step_x = int(img_width * (1 - overlap_ratio))
        step_y = int(img_height * (1 - overlap_ratio))
        
        print(f"估算步长: x={step_x}, y={step_y}")
        
        # 采样关键图像对进行精确计算
        key_pairs = self._select_key_pairs(grid_positions, num_cols, num_rows)
        
        print(f"计算 {len(key_pairs)} 个关键图像对...")
        
        # 计算关键位移
        displacements = {}
        for (idx1, idx2, direction) in tqdm(key_pairs, desc="计算位移"):
            displacement = self._compute_displacement_fast(gray_images[idx1], gray_images[idx2])
            displacements[(idx1, idx2)] = displacement
        
        # 基于关键位移和网格位置计算最终位置
        positions = np.zeros((len(images), 2), dtype=int)
        
        for i, (row, col) in enumerate(grid_positions):
            # 基础网格位置
            base_y = row * step_y
            base_x = col * step_x
            
            # 应用精确位移修正（如果有的话）
            # 这里简化处理，主要使用网格位置
            positions[i] = [base_y, base_x]
        
        print(f"位置计算完成")
        return positions
    
    def _select_key_pairs(self, positions: List[Tuple[int, int]], num_cols: int, num_rows: int) -> List[Tuple[int, int, str]]:
        """选择关键图像对进行精确计算"""
        pos_to_idx = {pos: i for i, pos in enumerate(positions)}
        key_pairs = []
        
        # 选择每行的第一个水平对和每列的第一个垂直对
        for row in range(num_rows):
            for col in range(num_cols - 1):
                if (row, col) in pos_to_idx and (row, col + 1) in pos_to_idx:
                    idx1 = pos_to_idx[(row, col)]
                    idx2 = pos_to_idx[(row, col + 1)]
                    key_pairs.append((idx1, idx2, 'horizontal'))
                    break  # 每行只取一个
        
        for col in range(num_cols):
            for row in range(num_rows - 1):
                if (row, col) in pos_to_idx and (row + 1, col) in pos_to_idx:
                    idx1 = pos_to_idx[(row, col)]
                    idx2 = pos_to_idx[(row + 1, col)]
                    key_pairs.append((idx1, idx2, 'vertical'))
                    break  # 每列只取一个
        
        return key_pairs
    
    def _compute_displacement_fast(self, img1: np.ndarray, img2: np.ndarray) -> Tuple[int, int]:
        """快速计算两图像间位移"""
        # 降采样以提高速度
        scale = 4
        small_img1 = img1[::scale, ::scale]
        small_img2 = img2[::scale, ::scale]
        
        # 相位相关
        F1 = np.fft.fft2(small_img1.astype(np.float32))
        F2 = np.fft.fft2(small_img2.astype(np.float32))
        
        cross_power = F1 * np.conjugate(F2)
        magnitude = np.abs(cross_power)
        magnitude[magnitude == 0] = 1e-10
        
        correlation = np.fft.ifft2(cross_power / magnitude).real
        
        # 找峰值
        peak_y, peak_x = np.unravel_index(np.argmax(correlation), correlation.shape)
        
        # 转换为位移
        h, w = correlation.shape
        if peak_y > h // 2:
            peak_y -= h
        if peak_x > w // 2:
            peak_x -= w
        
        # 放大回原尺寸
        return peak_y * scale, peak_x * scale
    
    def _create_stitched_image_fast(self, images: np.ndarray, positions: np.ndarray, output_path: str):
        """快速创建拼接图像"""
        
        # 计算输出尺寸
        if images.ndim == 4:
            img_height, img_width = images.shape[1], images.shape[2]
            is_color = True
        else:
            img_height, img_width = images.shape[1], images.shape[2]
            is_color = False
        
        max_y = positions[:, 0].max() + img_height
        max_x = positions[:, 1].max() + img_width
        
        print(f"输出尺寸: {max_x} x {max_y}")
        
        # 创建输出图像
        if is_color:
            output = np.zeros((max_y, max_x, 3), dtype=np.uint8)
        else:
            output = np.zeros((max_y, max_x), dtype=np.uint8)
        
        # 拼接图像
        for i, (y_pos, x_pos) in enumerate(tqdm(positions, desc="拼接")):
            y_end = min(y_pos + img_height, max_y)
            x_end = min(x_pos + img_width, max_x)
            
            if is_color:
                output[y_pos:y_end, x_pos:x_end] = images[i][:y_end-y_pos, :x_end-x_pos]
            else:
                output[y_pos:y_end, x_pos:x_end] = images[i][:y_end-y_pos, :x_end-x_pos]
        
        # 保存
        if is_color:
            result_img = Image.fromarray(output, 'RGB')
        else:
            result_img = Image.fromarray(output, 'L')
        
        result_img.save(output_path)
        print(f"已保存: {output_path}")
        
        # 创建缩略图
        thumbnail_path = output_path.replace('.png', '_thumb.png')
        result_img.thumbnail((2000, 2000), Image.Resampling.LANCZOS)
        result_img.save(thumbnail_path)
        print(f"缩略图: {thumbnail_path}")


def main():
    """主函数"""
    folder_path = r"D:\images\image_55"
    
    if not os.path.exists(folder_path):
        print(f"错误: 文件夹 {folder_path} 不存在")
        return
    
    # 创建快速拼接器
    stitcher = FastImageStitcher(ncc_threshold=0.2, max_workers=4)
    
    # 执行拼接
    positions = stitcher.quick_stitch(
        folder_path=folder_path,
        num_cols=16,
        num_rows=14,
        preserve_color=True,
        output_path="fast_stitched_result.png"
    )
    
    print("拼接完成！")


if __name__ == "__main__":
    main()
